server:
  port: 8080

spring:
  profiles:
    active: dev
  main:
    allow-circular-references: true
  datasource:
    druid:
      driver-class-name: ${sky.datasource.driver-class-name}
      url: jdbc:mysql://${sky.datasource.host}:${sky.datasource.port}/${sky.datasource.database}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true
      username: ${sky.datasource.username}
      password: ${sky.datasource.password}
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  # redis配置
  redis:
    host: ${sky.redis.host}
    port: ${sky.redis.port}
    password: ${sky.redis.password}
    username: ${sky.redis.username}
    database: ${sky.redis.database}
    ssl: ${sky.redis.ssl}

mybatis:
  #mapper配置文件
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.sky.entity
  configuration:
    #开启驼峰命名
    map-underscore-to-camel-case: true

logging:
  level:
    com:
      sky:
        mapper: debug
        service: info
        controller: info

sky:
  jwt:
    # 设置jwt签名加密时使用的秘钥
    admin-secret-key: itcast
    # 设置jwt过期时间 我改的非常久了省的总要获取
    admin-ttl: 72000000000
    # 设置前端传递过来的令牌名称
    admin-token-name: token
    # 设置jwt签名加密时使用的秘钥
    user-secret-key: itheima
    # 设置jwt过期时间 我改的非常久了省的总要获取
    user-ttl: 72000000000
    # 设置前端传递过来的令牌名称
    user-token-name: authentication
  # 文件上传配置
  upload:
    # 文件访问URL前缀
    url-prefix: /upload/
    # 文件访问基础URL（开发环境需要端口，生产环境可能不需要）
    base-url: http://localhost:8080
  wechat:
    appid: ${sky.wechat.appid}
    secret: ${sky.wechat.secret}