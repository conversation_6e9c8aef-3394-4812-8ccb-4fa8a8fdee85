<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.SetmealMapper">

    <update id="update">
        update setmeal
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="image != null and image != ''">
                image = #{image},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--套餐分页查询-->
    <!--SetmealVO需要套餐所属分类名字应该多表联查-->
    <select id="pageQuery" resultType="com.sky.vo.SetmealVO">
        select s.*, c.name as categoryName
        from setmeal s
        left join category c on s.category_id = c.id
        <where>
            <if test="name != null and name != ''">
                and s.name like concat('%',#{name},'%')
            </if>
            <if test="categoryId != null">
                and s.category_id = #{categoryId}
            </if>
            <if test="status != null">
                and s.status = #{status}
            </if>
        </where>
        order by s.update_time desc
    </select>

    <select id="countByStatusAndIds" resultType="java.lang.Long">
        select count(id)
        from setmeal
        where status = #{status}
        and id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <delete id="deleteBatch">
        delete from setmeal where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>
</mapper>