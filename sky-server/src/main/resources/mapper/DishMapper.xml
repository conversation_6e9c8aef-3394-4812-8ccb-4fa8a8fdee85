<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.DishMapper">

    <!--DishVO中还需要当前菜品关联的口味数据-->
    <resultMap id="DishVOResultMap" type="com.sky.vo.DishVO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="categoryId" column="category_id"/>
        <result property="price" column="price"/>
        <result property="image" column="image"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="updateTime" column="update_time"/>
        <result property="categoryName" column="categoryName"/>
        <collection property="flavors" ofType="com.sky.entity.DishFlavor">
            <id property="id" column="id"/>
            <result property="dishId" column="dish_id"/>
            <result property="name" column="name"/>
            <result property="value" column="value"/>
        </collection>
    </resultMap>

    <select id="pageQuery" resultMap="DishVOResultMap">
        select d.*, c.name as categoryName, df.*
        from dish d
        left join category c on d.category_id = c.id
        left join dish_flavor df on d.id = df.dish_id
        <where>
            <if test="name != null and name != ''">
                and d.name like concat('%',#{name},'%')
            </if>
            <if test="categoryId != null">
                and d.category_id = #{categoryId}
            </if>
            <if test="status != null">
                    and d.status = #{status}
            </if>
        </where>
        order by d.update_time desc
    </select>

    <select id="countByStatusAndIds" resultType="java.lang.Long">
        select count(id)
        from dish
        where status = #{status}
        and id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <delete id="deleteBatch">
        delete from dish where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <update id="update">
        update dish
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="image != null and image != ''">
                image = #{image},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>