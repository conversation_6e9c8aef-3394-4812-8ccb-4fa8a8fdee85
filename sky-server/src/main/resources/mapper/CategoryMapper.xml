<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.CategoryMapper">

    <select id="pageQuery" resultType="com.sky.entity.Category">
        select * from category
        <where>
            <if test="name != null and name != ''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
        </where>
        order by sort, create_time desc
    </select>

    <update id="update">
        update category
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="list" resultType="com.sky.entity.Category">
        select * from category
        <where>
            <if test="type != null">
                and type = #{type}
            </if>
            and status = 1
        </where>
        order by sort
    </select>
</mapper>