<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.AddressBookMapper">

    <select id="list" resultType="com.sky.entity.AddressBook">
        select * from address_book
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="isDefault != null">
                and is_default = #{isDefault}
            </if>
            <if test="consignee != null and consignee != ''">
                and consignee like concat('%',#{consignee},'%')
            </if>
            <if test="phone != null and phone != ''">
                and phone like concat('%',#{phone},'%')
            </if>
        </where>
        order by is_default desc
    </select>

    <update id="update">
        update address_book
        <set>
            <if test="consignee != null and consignee != ''">
                consignee = #{consignee},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="sex != null and sex != ''">
                sex = #{sex},
            </if>
            <if test="provinceCode != null and provinceCode != ''">
                province_code = #{provinceCode},
            </if>
            <if test="provinceName != null and provinceName != ''">
                province_name = #{provinceName},
            </if>
            <if test="cityCode != null and cityCode != ''">
                city_code = #{cityCode},
            </if>
            <if test="cityName != null and cityName != ''">
                city_name = #{cityName},
            </if>
            <if test="districtCode != null and districtCode != ''">
                district_code = #{districtCode},
            </if>
            <if test="districtName != null and districtName != ''">
                district_name = #{districtName},
            </if>
            <if test="detail != null and detail != ''">
                detail = #{detail},
            </if>
            <if test="label != null and label != ''">
                label = #{label},
            </if>
            <if test="isDefault != null">
                is_default = #{isDefault},
            </if>
        </set>
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
        </where>
    </update>
</mapper>