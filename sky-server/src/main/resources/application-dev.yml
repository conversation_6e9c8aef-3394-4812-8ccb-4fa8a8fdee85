sky:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    host: mysql.sqlpub.com
    port: 3306
    database: shaunsql
    username: shaunhighmore
    password: lBMdf65xDzAJNsS8
  # redis配置
  redis:
    host: dynamic-duckling-17191.upstash.io
    port: 6379
    # upstash平台redis生成的token
    password: AUMnAAIjcDE1ZmVjZjJiYTJhYzc0ZjUwODJjZTc1ZTMzYjJmYTdmNnAxMA
    # 用户名
    username: default
    # 一共有[0-15]16个数据库 database选用
    database: 0
    # 开启ssl ssl: true 是必须的，因为 Upstash 默认强制使用加密连接（rediss://）
    ssl: true
  wechat:
    appid: wxec50f97a74c6ebcf
    secret: 597e40dec4e9460a3d24c74018b6d2ee