package com.sky.mapper;

import com.sky.entity.SetmealDish;
import com.sky.vo.DishItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 套餐菜品Mapper
 */
@Mapper
public interface SetmealDishMapper {
    /**
     * 根据菜品id统计套餐菜品数量
     *
     * @param dishIds
     * @return
     */
    Long countByDishIds(@Param("dishIds") List<Long> dishIds);

    /**
     * 根据菜品id查询套餐id
     *
     * @param ids
     * @return
     */
    List<Long> getSetmealIdsByDishIds(@Param("ids") List<Long> ids);

    /**
     * 批量插入套餐菜品数据
     *
     * @param setmealDishes
     */
    void insert(@Param("setmealDishes") List<SetmealDish> setmealDishes);

    /**
     * 根据套餐id删除套餐菜品数据
     *
     * @param ids
     */
    void deleteBatch(@Param("ids") List<Long> ids);

    /**
     * 在setmeal_dish表中根据套餐id查询套餐菜品关联数据
     *
     * @param id
     * @return
     */
    @Select("select * from setmeal_dish where setmeal_id = #{id}")
    List<SetmealDish> getBySetmealId(Long id);

    /**
     * 在setmeal_dish表中根据套餐id查询菜品id
     *
     * @param id
     * @return
     */
    @Select("select dish_id from setmeal_dish where setmeal_id = #{id}")
    List<Long> getDishIdsBySetmealId(Long id);

    /**
     * 根据套餐id查询套餐关联的菜品
     *
     * @param id
     * @return
     */
    @Select("select sd.name, sd.copies, d.image, d.description from setmeal_dish sd left join dish d on sd.dish_id = d.id where sd.setmeal_id = #{id}")
    List<DishItemVO> getDishItemById(Long id);
}
