package com.sky.mapper;

import com.sky.entity.AddressBook;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 地址簿Mapper
 */
@Mapper
public interface AddressBookMapper {
    /**
     * 插入地址簿数据
     * @param addressBook
     */
    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into address_book (user_id, consignee, phone, sex, province_code, province_name, city_code, city_name, district_code, district_name, detail, label, is_default) " +
            "values (#{userId}, #{consignee}, #{phone}, #{sex}, #{provinceCode}, #{provinceName}, #{cityCode}, #{cityName}, #{districtCode}, #{districtName}, #{detail}, #{label}, #{isDefault})")
    void insert(AddressBook addressBook);

    /**
     * 条件查询地址簿数据
     * @param addressBook
     * @return
     */
    List<AddressBook> list(AddressBook addressBook);

    /**
     * 根据id修改地址簿数据
     * @param addressBook
     */
    void update(AddressBook addressBook);

    /**
     * 根据id删除地址簿数据
     * @param id
     */
    @Select("delete from address_book where id = #{id}")
    void delete(Long id);
}
