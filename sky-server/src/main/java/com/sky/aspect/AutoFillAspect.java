package com.sky.aspect;

import com.sky.annotation.AutoFill;
import com.sky.constant.AutoFillConstant;
import com.sky.context.BaseContext;
import com.sky.enumeration.OperationType;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 为公共字段填充的切面类
 */
@Aspect
@Component
@Slf4j
public class AutoFillAspect {
    //编写切入点表达式PointCut
    //扫描mapper包下所有的方法 找到标有AutoFill注解的方法进行切入
    @Pointcut("execution(* com.sky.mapper.*.*(..)) && @annotation(com.sky.annotation.AutoFill)")
    public void autoFillPointCut() {
    }

    //需要在执行insert和update方法之前执行
    //编写前置通知方法
    @Before("autoFillPointCut()")
    public void autoFill(JoinPoint joinPoint) {
        log.info("执行了自动填充切面");
        //获取mapper方法AutoFill注解标记的值 看看是insert还是update
        //获取到目标方法的签名对象
        //向下转型
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //获取方法上的注解
        AutoFill autoFill = signature.getMethod().getAnnotation(AutoFill.class);
        //获取注解的值
        OperationType operationType = autoFill.value();

        //获取被自动填充字段的实体类对象
        //获取方法的参数列表
        Object[] args = joinPoint.getArgs();
        //判断参数列表是否为空
        if (args == null || args.length == 0) {
            //为空就不用填充了
            return;
        }
        //这里约定第一个参数是需要被自动填充的实体类
        Object entity = args[0];

        //准备填充公共字段的数据 需要填充的公共字段就是进行操作的用户id与执行操作时的日期时间
        Long currentId = BaseContext.getCurrentId();
        LocalDateTime now = LocalDateTime.now();

        //为不同的数据库操作执行不同的公共字段填充
        //根据insert还是update来决定是填充创建与更新还是只填充更新
        if (operationType == OperationType.INSERT) {
            //insert操作
            //为entity对象填充createTime和updateTime
            //为entity对象填充createUser和updateUser
            //反射 先获取方法然后传参调用
            try {
                //填充创建时间
                entity.getClass().getDeclaredMethod(AutoFillConstant.SET_CREATE_TIME, LocalDateTime.class).invoke(entity, now);
                //填充更新时间
                entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_TIME, LocalDateTime.class).invoke(entity, now);
                //填充创建人
                entity.getClass().getDeclaredMethod(AutoFillConstant.SET_CREATE_USER, Long.class).invoke(entity, currentId);
                //填充更新人
                entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_USER, Long.class).invoke(entity, currentId);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        }else if(operationType == OperationType.UPDATE){
            //update操作
            //为entity对象填充updateTime
            //为entity对象填充updateUser
            try {
                //填充更新时间
                entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_TIME, LocalDateTime.class).invoke(entity, now);
                //填充更新人
                entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_USER, Long.class).invoke(entity, currentId);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }
}
