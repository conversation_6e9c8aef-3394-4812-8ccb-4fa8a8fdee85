//准备营业额数据
        //select sum(amount) from orders where order_time > begin and order_time < end and status = 5 group by date_format(order_time,'%Y-%m-%d')
        Map<String, Object> map = new HashMap();
        LocalDateTime beginTime = LocalDateTime.of(begin, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(end, LocalTime.MAX);
        map.put("begin", beginTime);
        map.put("end", endTime);
        map.put("status", Orders.COMPLETED);
        List<Double> turnoverList = orderMapper.getTurnoverStatistics(map);
        //没有营业额的日期需要填充0.0
        for(int i = turnoverList.size(); i < dateList.size(); i++){
            turnoverList.add(0.0);
        }