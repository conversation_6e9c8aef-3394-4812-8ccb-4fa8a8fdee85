package com.sky.service.impl;

import com.sky.entity.Orders;
import com.sky.mapper.OrderMapper;
import com.sky.service.ReportService;
import com.sky.vo.TurnoverReportVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ReportServiceImpl implements ReportService {

    @Autowired
    private OrderMapper orderMapper;


    /**
     * 营业额统计
     * @param begin
     * @param end
     * @return
     */
    public TurnoverReportVO getTurnoverStatistics(LocalDate begin, LocalDate end) {
        //准备日期数据
        List<LocalDate> dateList = new ArrayList<>();
        dateList.add(begin);
        LocalDate beginTemp = begin;
        while(!beginTemp.equals(end)){
            //日期计算，获得指定日期后1天的日期
            beginTemp = beginTemp.plusDays(1);
            dateList.add(beginTemp);
        }

        //准备营业额数据
        //SELECT DATE(order_time) as order_date, SUM(amount) as daily_turnover
        //FROM orders
        //WHERE order_time BETWEEN ? AND ?
        //  AND status = 5
        //GROUP BY DATE(order_time)
        Map<String, Object> map = new HashMap();
        LocalDateTime beginTime = LocalDateTime.of(begin, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(end, LocalTime.MAX);
        map.put("begin", beginTime);
        map.put("end", endTime);
        map.put("status", Orders.COMPLETED);
        Map<LocalDate, Double> turnoverMap = orderMapper.getTurnoverStatisticsOfDay(map);
        List<Double> turnoverList = new ArrayList<>();
        for(LocalDate date : dateList){
            //营业额可能为null，使用自动拆箱，有空指针异常的风险
            //解决方案：转成对象类型，手动拆箱，指定默认值
            double turnover = turnoverMap.getOrDefault(date, 0.0);
            turnoverList.add(turnover);
        }

        //封装数据
        return TurnoverReportVO.builder()
                .dateList(StringUtils.join(dateList, ","))
                .turnoverList(StringUtils.join(turnoverList, ","))
                .build();
    }
}
