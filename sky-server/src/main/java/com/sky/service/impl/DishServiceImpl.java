package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.constant.MessageConstant;
import com.sky.constant.StatusConstant;
import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.entity.DishFlavor;
import com.sky.entity.Setmeal;
import com.sky.exception.DeletionNotAllowedException;
import com.sky.mapper.DishFlavorMapper;
import com.sky.mapper.DishMapper;
import com.sky.mapper.SetmealDishMapper;
import com.sky.mapper.SetmealMapper;
import com.sky.result.PageResult;
import com.sky.service.DishService;
import com.sky.vo.DishVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DishServiceImpl implements DishService {

    @Autowired
    private DishMapper dishMapper;
    @Autowired
    private DishFlavorMapper dishFlavorMapper;
    @Autowired
    private SetmealDishMapper setmealDishMapper;
    @Autowired
    private SetmealMapper setmealMapper;

    /**
     * 新增菜品
     * 添加菜品同时需要添加对应的口味数据，需要向两张表同时插入数据：dish、dish_flavor
     *
     * @param dishDTO
     */
    //涉及多个数据库操作需要保证方法原子性故开启事物
    //任何一个操作失败整个都表示整个操作失败
    @Transactional
    public void saveWithFlavor(DishDTO dishDTO) {
        //准备与数据库交互的Dish对象
        Dish dish = new Dish();
        BeanUtils.copyProperties(dishDTO, dish);
        log.info("Dish:{}", dish);
        //插入1条菜品数据
        dishMapper.insert(dish);

        //批量一次性插入[0,n]条菜品口味数据，操作dish_flavor表
        insertDishFlavors(dishDTO, dish.getId());
    }

    /**
     * 菜品分页查询
     *
     * @param dishPageQueryDTO
     * @return
     */
    public PageResult pageQuery(DishPageQueryDTO dishPageQueryDTO) {
        //开启分页
        PageHelper.startPage(dishPageQueryDTO.getPage(), dishPageQueryDTO.getPageSize());
        //执行查询
        //因为DishPageQueryDTO中有分类的id前端需要我们封装的结果中有分类名称所以这里应该是一个多表联查
        //甚至DishVO中还需要当前菜品关联的口味数据
        Page<DishVO> page = dishMapper.pageQuery(dishPageQueryDTO);
        //返回结果
        return new PageResult(page.getTotal(), page.getResult());
    }
    /**
     * 菜品批量删除
     *
     * @param ids
     */
    @Transactional
    public void deleteBatch(List<Long> ids) {
        //ids的元素个数
        int size = ids.size();
        //是否存在起售中的菜品
        //直接统计 id in ids 且 status = 0 的记录数
        Long count = dishMapper.countByStatusAndIds(StatusConstant.DISABLE, ids);
        if (count < size) {
            throw new DeletionNotAllowedException(MessageConstant.DISH_ON_SALE);
        }
        //是否存在被套餐关联的菜品
        //在setmeal_dish表中统计 dish_id in ids 的记录数 只要记录数大于0就表示有被套餐关联不允许删除
        count = setmealDishMapper.countByDishIds(ids);
        if (count > 0) {
            throw new DeletionNotAllowedException(MessageConstant.DISH_BE_RELATED_BY_SETMEAL);
        }
        //开始删除
        //先删除菜品关联的口味
        dishFlavorMapper.deleteByDishIds(ids);
        //删除菜品
        dishMapper.deleteBatch(ids);
    }

    /**
     * 根据id查询菜品和对应的口味数据
     *
     * @param id
     * @return
     */
    @Override
    public DishVO getByIdWithFlavor(Long id) {
        //根据id查询在dish表中的菜品数据
        Dish dish = dishMapper.getById(id);
        //根据菜品表id查询在dish_flavor表中的口味数据
        List<DishFlavor> dishFlavors = dishFlavorMapper.getByDishId(id);

        //封装返回到DishVO中
        DishVO dishVO = new DishVO();
        BeanUtils.copyProperties(dish, dishVO);
        dishVO.setFlavors(dishFlavors);
        return dishVO;
    }

    /**
     * 修改菜品以及口味
     * @param dishDTO
     */
    @Transactional
    public void updateWithFlavor(DishDTO dishDTO) {
        //先删除菜品的口味数据
        dishFlavorMapper.deleteByDishIds(Collections.singletonList(dishDTO.getId()));
        //再插入菜品的口味数据
        insertDishFlavors(dishDTO, dishDTO.getId());
        //最后更新菜品数据
        Dish dish = new Dish();
        BeanUtils.copyProperties(dishDTO, dish);
        dishMapper.update(dish);
    }

    /**
     * 启用禁用菜品
     * @param status
     * @param id
     */
    @Transactional
    public void startOrStop(Integer status, Long id) {
        //若是要停用操作 还需要停用关联的套餐
        if(Objects.equals(status, StatusConstant.DISABLE)){
            //根据菜品id查询关联的套餐id
            List<Long> setmealIds = setmealDishMapper.getSetmealIdsByDishIds(Collections.singletonList(id));
            if(!setmealIds.isEmpty()){
                //修改关联套餐状态
                setmealIds.forEach(setmealId -> {
                    Setmeal setmeal = Setmeal.builder()
                            .status(StatusConstant.DISABLE)
                            .id(setmealId)
                            .build();
                    setmealMapper.update(setmeal);
                });
            }
        }
        //修改菜品状态
        Dish dish = Dish.builder()
                .status(status)
                .id(id)
                .build();
        dishMapper.update(dish);
    }

    /**
     * 根据分类id查询菜品
     * @param categoryId
     * @return
     */
    public List<Dish> list(Long categoryId) {
        //查询dish表中category_id = categoryId 且 status = 1的记录
        return dishMapper.list(categoryId);
    }

    /**
     * 根据条件 去查询菜品数据，包含口味数据
     * @param categoryId
     * @return
     */
    public List<DishVO> listWithFlavor(Long categoryId) {
        //查询dish表中category_id = categoryId 且 status = 1的记录
        List<Dish> dishList = dishMapper.list(categoryId);
        //查询dish_flavor表中dish_id = dish.id的记录
        List<DishVO> dishVOList = new ArrayList<>();
        for (Dish dish : dishList) {
            DishVO dishVO = new DishVO();
            BeanUtils.copyProperties(dish, dishVO);
            List<DishFlavor> dishFlavors = dishFlavorMapper.getByDishId(dish.getId());
            dishVO.setFlavors(dishFlavors);
            dishVOList.add(dishVO);
        }
        return dishVOList;
    }


    /**
     * 插入菜品口味数据
     *
     * @param dishDTO 菜品DTO
     * @param dishId 菜品ID
     */
    private void insertDishFlavors(DishDTO dishDTO, Long dishId) {
        // 方案一：使用map()替代peek()（推荐）
        List<DishFlavor> flavors = Optional.ofNullable(dishDTO.getFlavors())
                .orElse(Collections.emptyList())
                .stream()
                .filter(flavor -> flavor != null) // 过滤null元素
                .map(flavor -> {
                    flavor.setDishId(dishId);
                    return flavor;
                })
                .collect(Collectors.toList());

        // 只有当口味列表不为空时才执行批量插入
        if (!flavors.isEmpty()) {
            dishFlavorMapper.insertBatch(flavors);
            log.info("成功插入{}条菜品口味数据", flavors.size());
        } else {
            log.info("该菜品无口味数据，跳过口味插入");
        }
    }

    /**
     * 插入菜品口味数据 - 方案二：forEach方式（更直观）
     */
    private void insertDishFlavorsAlternative(DishDTO dishDTO, Long dishId) {
        List<DishFlavor> flavors = Optional.ofNullable(dishDTO.getFlavors())
                .orElse(Collections.emptyList());

        if (!flavors.isEmpty()) {
            // 使用forEach设置dishId
            flavors.forEach(flavor -> flavor.setDishId(dishId));
            dishFlavorMapper.insertBatch(flavors);
            log.info("成功插入{}条菜品口味数据", flavors.size());
        } else {
            log.info("该菜品无口味数据，跳过口味插入");
        }
    }
}
