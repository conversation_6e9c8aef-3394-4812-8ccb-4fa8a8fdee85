package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.constant.MessageConstant;
import com.sky.constant.StatusConstant;
import com.sky.dto.SetmealDTO;
import com.sky.dto.SetmealPageQueryDTO;
import com.sky.entity.Setmeal;
import com.sky.entity.SetmealDish;
import com.sky.exception.DeletionNotAllowedException;
import com.sky.exception.SetmealEnableFailedException;
import com.sky.mapper.DishMapper;
import com.sky.mapper.SetmealDishMapper;
import com.sky.mapper.SetmealMapper;
import com.sky.result.PageResult;
import com.sky.service.SetmealService;
import com.sky.vo.DishItemVO;
import com.sky.vo.SetmealVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 套餐服务
 */
@Service
@Slf4j
public class SetmealServiceImpl implements SetmealService {

    @Autowired
    private SetmealMapper setmealMapper;
    @Autowired
    private SetmealDishMapper setmealDishMapper;
    @Autowired
    private DishMapper dishMapper;

    /**
     * 新增套餐
     * @param setmealDTO
     */
    @Transactional
    public void saveWithDish(SetmealDTO setmealDTO) {
        //插入套餐数据
        Setmeal setmeal = new Setmeal();
        BeanUtils.copyProperties(setmealDTO, setmeal);
        //套餐id插入后需要回填
        setmealMapper.insert(setmeal);
        //向setmeal_dish表插入数据 套餐下关联的菜品
        List<SetmealDish> setmealDishes = setmealDTO.getSetmealDishes();
        Long setmealId = setmeal.getId();
        //回填的套餐id 设置到setmealDish中
        setmealDishes.forEach(setmealDish -> setmealDish.setSetmealId(setmealId));
        //insert into setmeal_dish() values(),(),()
        setmealDishMapper.insert(setmealDishes);
    }

    /**
     * 套餐分页查询
     * @param setmealPageQueryDTO
     * @return
     */
    public PageResult pageQuery(SetmealPageQueryDTO setmealPageQueryDTO) {
        //开启分页
        PageHelper.startPage(setmealPageQueryDTO.getPage(), setmealPageQueryDTO.getPageSize());
        //执行查询
        //前端需要我们封装的结果中有分类名称
        //这里应该是一个多表联查
        Page<SetmealVO> page = setmealMapper.pageQuery(setmealPageQueryDTO);
        //返回结果
        return new PageResult(page.getTotal(), page.getResult());
    }

    /**
     * 套餐批量删除
     * @param ids
     */
    @Transactional
    public void deleteBatch(List<Long> ids) {
        //判断套餐是否启售
        //查询id in ids 且 status = 1 的记录数
        Long count = setmealMapper.countByStatusAndIds(StatusConstant.ENABLE, ids);
        if (count > 0) {
            throw new DeletionNotAllowedException(MessageConstant.SETMEAL_ON_SALE);
        }
        //删除套餐数据
        setmealMapper.deleteBatch(ids);
        //删除套餐菜品数据
        setmealDishMapper.deleteBatch(ids);
    }

    /**
     * 根据id查询套餐以及对应的菜品
     *
     * @param id
     * @return
     */
    public SetmealVO getByIdWithDish(Long id) {
        //查询套餐数据
        Setmeal setmeal = setmealMapper.getById(id);
        //查询套餐菜品数据
        List<SetmealDish> setmealDishes = setmealDishMapper.getBySetmealId(id);
        //封装到SetmealVO中
        SetmealVO setmealVO = new SetmealVO();
        BeanUtils.copyProperties(setmeal, setmealVO);
        setmealVO.setSetmealDishes(setmealDishes);
        //返回
        return setmealVO;
    }

    /**
     * 修改套餐
     * @param setmealDTO
     */
    @Transactional
    public void updateWithDish(SetmealDTO setmealDTO) {
        //修改套餐数据
        Setmeal setmeal = new Setmeal();
        BeanUtils.copyProperties(setmealDTO, setmeal);
        setmealMapper.update(setmeal);
        //修改套餐关联的菜品数据
        //先删除套餐菜品数据
        setmealDishMapper.deleteBatch(Collections.singletonList(setmealDTO.getId()));
        //再插入套餐菜品数据
        List<SetmealDish> setmealDishes = setmealDTO.getSetmealDishes();
        setmealDishes.forEach(setmealDish -> setmealDish.setSetmealId(setmealDTO.getId()));
        setmealDishMapper.insert(setmealDishes);
    }

    /**
     * 启用禁用套餐
     * @param status
     * @param id
     */
    @Transactional
    public void startOrStop(Integer status, Long id) {
        //若是要启售操作 还需要判断套餐内包含的菜品是否都启售
        if(Objects.equals(status, StatusConstant.ENABLE)){
            //查询套餐内包含的菜品是否都启售
            List<Long> dishIds = setmealDishMapper.getDishIdsBySetmealId(id);
            Long count = dishMapper.countByStatusAndIds(StatusConstant.ENABLE, dishIds);
            //dishIds.size()表示套餐内包含的菜品数量
            //count表示套餐内包含的已启售的菜品数量
            //如果count < dishIds.size()就表示套餐内包含的菜品有未启售的
            if(count < dishIds.size()){
                throw new SetmealEnableFailedException(MessageConstant.SETMEAL_ENABLE_FAILED);
            }
        }
        //套餐内包含的菜品都启售了，就可以启售套餐了,若是禁售套餐直接禁售即可
        //修改套餐状态
        Setmeal setmeal = Setmeal.builder()
                .status(status)
                .id(id)
                .build();
        setmealMapper.update(setmeal);
    }

    /**
     * 根据分类id查询套餐
     * @param categoryId
     * @return
     */
    public List<Setmeal> list(Long categoryId) {
        return setmealMapper.list(categoryId);
    }

    /**
     * 根据套餐id查询套餐关联的菜品
     * @param id
     * @return
     */
    public List<DishItemVO> getDishItemById(Long id) {
        //查询套餐关联的菜品
        return setmealDishMapper.getDishItemById(id);
    }
}
