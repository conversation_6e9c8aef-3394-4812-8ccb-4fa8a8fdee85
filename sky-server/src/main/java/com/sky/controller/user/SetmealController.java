package com.sky.controller.user;

import com.sky.constant.StatusConstant;
import com.sky.entity.Setmeal;
import com.sky.result.Result;
import com.sky.service.SetmealService;
import com.sky.vo.DishItemVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@RestController("userSetmealController")
@RequestMapping("/user/setmeal")
@Api(tags = "C端-套餐浏览接口")
public class SetmealController {
    @Autowired
    private SetmealService setmealService;

    /**
     * 根据分类id查询套餐
     *
     * @param categoryId
     * @return
     */
    @GetMapping("/list")
    @ApiOperation("根据分类id查询套餐")
    //注解形式开启缓存查询 有缓存查缓存没缓存查数据库然后存入缓存
    /*
    @Cacheable 说明:
	作用: 在方法执行前，spring先查看缓存中是否有数据，如果有数据，则直接返回缓存数据；若没有数据，调用方法并将方法返回值放到缓存中
	value: 缓存的名称，每个缓存名称下面可以有多个key
	key: 缓存的key  ----------> 支持Spring的表达式语言SPEL语法 这里key的语法可以点@Cacheable进去看 其实掌握一种"#形参变量名"即可
	value::key 在redis中相当于整个key的部分
     */
    @Cacheable(value = "setmealCache", key = "#categoryId")
    public Result<List<Setmeal>> list(Long categoryId) {
//        Setmeal setmeal = new Setmeal();
//        setmeal.setCategoryId(categoryId);
//        setmeal.setStatus(StatusConstant.ENABLE);

        List<Setmeal> list = setmealService.list(categoryId);
        return Result.success(list);
    }

    /**
     * 根据套餐id查询包含的菜品列表
     *
     * @param id
     * @return
     */
    @GetMapping("/dish/{id}")
    @ApiOperation("根据套餐id查询包含的菜品列表")
    public Result<List<DishItemVO>> dishList(@PathVariable("id") Long id) {
        List<DishItemVO> list = setmealService.getDishItemById(id);
        return Result.success(list);
    }
}
