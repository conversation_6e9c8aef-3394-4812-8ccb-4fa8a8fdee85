package com.sky.controller.admin;

import com.sky.dto.SetmealDTO;
import com.sky.dto.SetmealPageQueryDTO;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.SetmealService;
import com.sky.vo.SetmealVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 套餐管理
 */
@RestController("adminSetmealController")
@RequestMapping("/admin/setmeal")
@Slf4j
@Api(tags = "套餐相关接口")
public class SetmealController {

    @Autowired
    private SetmealService setmealService;

    /**
     * 新增套餐
     * @param setmealDTO
     * @return
     * 业务规则：
     *
     *  套餐名称唯一
     *  套餐必须属于某个分类
     *  套餐必须包含菜品
     *  名称、分类、价格、图片为必填项
     *  添加菜品窗口需要根据分类类型来展示菜品
     *  新增的套餐默认为停售状态
     */
    @ApiOperation("新增套餐")
    @PostMapping
    //注解形式移除新增套餐所属分类的缓存
    //指定key就是删除某个key对应的缓存数据
    @CacheEvict(value = "setmealCache", key = "#setmealDTO.categoryId")
    public Result save(@RequestBody SetmealDTO setmealDTO) {
        log.info("新增套餐：{}", setmealDTO);
        setmealService.saveWithDish(setmealDTO);
        return Result.success();
    }


    /**
     * 套餐分页查询
     * @param setmealPageQueryDTO
     * @return
     * 业务规则：
     *
     *  根据页码进行分页展示
     *  每页展示10条数据
     *  可以根据需要，按照套餐名称、分类、售卖状态进行查询
     */
    @ApiOperation("套餐分页查询")
    @GetMapping("/page")
    public Result<PageResult> page(SetmealPageQueryDTO setmealPageQueryDTO) {
        log.info("套餐分页查询：{}", setmealPageQueryDTO);
        PageResult pageResult = setmealService.pageQuery(setmealPageQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 删除套餐
     * @param ids
     * @return
     *
     * 业务规则：
     *
     *  可以一次删除一个套餐，也可以批量删除套餐
     *  起售中的套餐不能删除
     */
    @ApiOperation("删除套餐")
    @DeleteMapping
    //注解形式移除所有套餐的缓存
    //allEntries = true 表示删除指定value下的所有缓存
    @CacheEvict(value = "setmealCache", allEntries = true)
    public Result delete(@RequestParam List<Long> ids) {
        log.info("删除套餐：{}", ids);
        setmealService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 根据id查询套餐以及对应的菜品
     * @param id
     * @return
     */
    @ApiOperation("根据id查询套餐")
    @GetMapping("/{id}")
    public Result<SetmealVO> getById(@PathVariable Long id) {
        log.info("根据id查询套餐：{}", id);
        SetmealVO setmealVO = setmealService.getByIdWithDish(id);
        return Result.success(setmealVO);
    }

    /**
     * 修改套餐
     * @param setmealDTO
     * @return
     */
    @ApiOperation("修改套餐")
    @PutMapping
    //注解形式移除所有套餐的缓存
    @CacheEvict(value = "setmealCache", allEntries = true)
    public Result update(@RequestBody SetmealDTO setmealDTO) {
        log.info("修改套餐：{}", setmealDTO);
        setmealService.updateWithDish(setmealDTO);
        return Result.success();
    }

    /**
     * 启用禁用套餐
     * @param status
     * @param id
     * @return
     *
     * 业务规则：
     *
     *  套餐内包含未启售菜品，不能启售
     */
    @ApiOperation("修改套餐状态")
    @PostMapping("/status/{status}")
    //注解形式移除所有套餐的缓存
    @CacheEvict(value = "setmealCache", allEntries = true)
    public Result startOrStop(@PathVariable Integer status, @RequestParam("id") Long id) {
        log.info("修改套餐状态：{},{}", status, id);
        setmealService.startOrStop(status, id);
        return Result.success();
    }
}
