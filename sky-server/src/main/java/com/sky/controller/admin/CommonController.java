package com.sky.controller.admin;

import com.sky.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


import java.io.File;
import java.io.IOException;
import java.util.UUID;

/**
 * 通用接口
 */
@RestController
@RequestMapping("/admin/common")
@Api(tags = "通用接口")
@Slf4j
public class CommonController {

    @Value("${sky.upload.url-prefix:/upload/}")
    private String urlPrefix;

    @Value("${sky.upload.base-url:}")
    private String baseUrl;

    /**
     * 文件上传
     *
     * @param file 上传的文件
     * @return 文件访问路径
     */
    @PostMapping("/upload")
    @ApiOperation("文件上传")
    public Result<String> upload(@RequestParam("file") MultipartFile file) {
        log.info("文件上传开始，原始文件名：{}, 文件大小：{}KB",
                file.getOriginalFilename(),
                file.getSize() / 1024);

        try {
            // 参数校验
            if (file == null || file.isEmpty()) {
                log.warn("上传文件为空");
                return Result.error("上传文件不能为空");
            }

            // 文件大小校验（10MB）
            if (file.getSize() > 10 * 1024 * 1024) {
                log.warn("文件大小超过限制：{}KB", file.getSize() / 1024);
                return Result.error("文件大小不能超过10MB");
            }

            // 获取原始文件名和扩展名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                log.warn("文件名为空");
                return Result.error("文件名不能为空");
            }

            String extension = getFileExtension(originalFilename);
            if (extension.isEmpty()) {
                log.warn("文件没有扩展名：{}", originalFilename);
                return Result.error("文件必须有扩展名");
            }

            // 验证文件类型（只允许图片）
            if (!isImageFile(extension)) {
                log.warn("不支持的文件类型：{}", extension);
                return Result.error("只支持图片文件上传（jpg、jpeg、png、gif、bmp、webp）");
            }

            // 生成新的文件名：UUID + 扩展名
            String newFileName = UUID.randomUUID().toString().replace("-", "") + extension;

            // 确保上传目录存在
            File uploadDir = getUploadDirectory();
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                if (!created) {
                    log.error("创建上传目录失败：{}", uploadDir.getAbsolutePath());
                    return Result.error("创建上传目录失败");
                }
                log.info("创建上传目录：{}", uploadDir.getAbsolutePath());
            }

            // 保存文件
            File destFile = new File(uploadDir, newFileName);
            file.transferTo(destFile);

            // 构建文件访问URL
            String fileUrl = buildFileUrl(newFileName);
            log.info("文件上传成功 - 原文件名：{}, 新文件名：{}, 访问路径：{}",
                    originalFilename, newFileName, fileUrl);

            return Result.success(fileUrl);

        } catch (IOException e) {
            log.error("文件上传IO异常：{}", e.getMessage(), e);
            return Result.error("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("文件上传未知异常：{}", e.getMessage(), e);
            return Result.error("文件上传异常，请稍后重试");
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex).toLowerCase();
    }

    /**
     * 验证是否为图片文件
     */
    private boolean isImageFile(String extension) {
        String[] allowedExtensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"};
        for (String allowed : allowedExtensions) {
            if (allowed.equals(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取上传目录
     */
    private File getUploadDirectory() {
        // 获取项目根目录下的upload文件夹
        String projectPath = System.getProperty("user.dir");
        return new File(projectPath, "upload");
    }

    /**
     * 构建文件访问URL
     * 如果配置了baseUrl则返回完整URL，否则返回相对路径
     */
    private String buildFileUrl(String fileName) {
        if (baseUrl != null && !baseUrl.trim().isEmpty()) {
            // 返回完整URL（适用于开发环境或前后端分离）
            return baseUrl + urlPrefix + fileName;
        } else {
            // 返回相对路径（适用于生产环境或同域部署）
            return urlPrefix + fileName;
        }
    }
}
