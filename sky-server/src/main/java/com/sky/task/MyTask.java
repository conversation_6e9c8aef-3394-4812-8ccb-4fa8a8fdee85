package com.sky.task;

import com.sky.entity.Orders;
import com.sky.mapper.OrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 自定义定时任务
 * 定时任务类
 */
@Component
@Slf4j
public class MyTask {

    //注入订单mapper
    @Autowired
    private OrderMapper orderMapper;

    //处理超时待支付订单的定时任务方法
    //每分钟执行一次
    @Scheduled(cron = "0 * * * * ?")
    public void checkTimeoutOrder() {
        log.info("定时处理超时订单：{}", LocalDateTime.now());
        //查询订单状态为待支付且当前时间减去15分钟大于订单创建时间的订单

        //获取当前时间
        LocalDateTime now = LocalDateTime.now();
        //减少15分钟后时间
        LocalDateTime time = now.minusMinutes(15);

        //sql: select * from orders where status = ? and order_time < (当前时间 - 15分钟)
        List<Orders> orders = orderMapper.getByStatusAndOrderTimeLT(Orders.PENDING_PAYMENT, time);
        //修改订单状态为已取消
        if(orders != null && orders.size() > 0) {
            orders.forEach(order -> {
                order.setStatus(Orders.CANCELLED);
                order.setCancelTime(now);
                order.setCancelReason("订单超时，自动取消");
                orderMapper.update(order);
            });
        }
    }

    //凌晨1点处理订单状态为派送中且订单创建时间小于当前时间减去1小时的订单 修改订单状态为已完成
    @Scheduled(cron = "0 0 1 * * ?")
    public void checkTimeoutDeliveryOrder() {
        log.info("定时处理派送中订单：{}", LocalDateTime.now());
        //查询订单状态为派送中且当前时间减去1小时大于订单创建时间的订单
        //获取当前时间
        LocalDateTime now = LocalDateTime.now();
        //减少1小时后时间
        LocalDateTime time = now.minusHours(1);
        List<Orders> orders = orderMapper.getByStatusAndOrderTimeLT(Orders.DELIVERY_IN_PROGRESS, time);
        //修改订单状态为已完成
        if(orders != null && orders.size() > 0) {
            orders.forEach(order -> {
                order.setStatus(Orders.COMPLETED);
                orderMapper.update(order);
            });
        }
    }

}
