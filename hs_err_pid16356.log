#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32744 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=16356, tid=5228
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.7+9-895.130-jcef (21.0.7+9) (build 21.0.7+9-b895.130)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.7+9-895.130-jcef (21.0.7+9-b895.130, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Djava.rmi.server.hostname=127.0.0.1 -Duser.timezone=UTC -Xmx2048m -Xms128m -Dhttp.proxyHost=127.0.0.1 -Dhttp.proxyPort=10809 -Dhttps.proxyHost=127.0.0.1 -Dhttps.proxyPort=10809 -Dhttp.useProxy=true -Dhttp.nonProxyHosts=localhost|127.0.0.1|::1 -Dhttps.nonProxyHosts=localhost|127.0.0.1|::1 -DsocksNonProxyHosts=localhost|127.0.0.1|::1 -Djdbc.classpath=C:\Users\<USER>\AppData\Local\Temp\RemoteDriver5.classpath.txt --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.desktop/java.awt.peer=ALL-UNNAMED --add-opens=java.desktop/java.awt=ALL-UNNAMED --add-opens=jdk.unsupported/sun.misc=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 com.intellij.database.remote.RemoteJdbcServer com.mysql.cj.jdbc.Driver

Host: Intel(R) Core(TM) i3-9100F CPU @ 3.60GHz, 4 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.3636)
Time: Sat Jul 12 00:43:12 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.3636) elapsed time: 631.371708 seconds (0d 0h 10m 31s)

---------------  T H R E A D  ---------------

Current thread (0x0000019a23faece0):  JavaThread "C1 CompilerThread0" daemon [_thread_in_vm, id=5228, stack(0x00000079bd500000,0x00000079bd600000) (1024K)]


Current CompileTask:
C1:631371 2482   !   3       sun.rmi.transport.Transport::serviceCall (370 bytes)

Stack: [0x00000079bd500000,0x00000079bd600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e6049]
V  [jvm.dll+0x8c4343]
V  [jvm.dll+0x8c689e]
V  [jvm.dll+0x8c6f83]
V  [jvm.dll+0x289266]
V  [jvm.dll+0xc675d]
V  [jvm.dll+0xc6c93]
V  [jvm.dll+0x1fe605]
V  [jvm.dll+0x1f90e8]
V  [jvm.dll+0x16a256]
V  [jvm.dll+0x16a0a6]
V  [jvm.dll+0x161629]
V  [jvm.dll+0x163bc1]
V  [jvm.dll+0x161ec4]
V  [jvm.dll+0x16ac04]
V  [jvm.dll+0x16a0a6]
V  [jvm.dll+0x161629]
V  [jvm.dll+0x163bc1]
V  [jvm.dll+0x161ec4]
V  [jvm.dll+0x15b9d0]
V  [jvm.dll+0x16bdf6]
V  [jvm.dll+0x156dc0]
V  [jvm.dll+0x157143]
V  [jvm.dll+0x157406]
V  [jvm.dll+0x15677f]
V  [jvm.dll+0x1585a1]
V  [jvm.dll+0x25f76c]
V  [jvm.dll+0x25dcb6]
V  [jvm.dll+0x3ffa86]
V  [jvm.dll+0x86bea8]
V  [jvm.dll+0x6e480d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17344]
C  [ntdll.dll+0x526b1]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000019a65c9e3e0, length=20, elements={
0x0000019a0c278fa0, 0x0000019a23f8be40, 0x0000019a23f8ee10, 0x0000019a23f909d0,
0x0000019a23f91420, 0x0000019a23f91e70, 0x0000019a23f928c0, 0x0000019a23f93640,
0x0000019a23faece0, 0x0000019a65205e70, 0x0000019a6521baf0, 0x0000019a654f7570,
0x0000019a655fe7a0, 0x0000019a655faab0, 0x0000019a655fb110, 0x0000019a655fb770,
0x0000019a656fcaf0, 0x0000019a65885f20, 0x0000019a65885890, 0x0000019a65885200
}

Java Threads: ( => current thread )
  0x0000019a0c278fa0 JavaThread "main"                              [_thread_blocked, id=15232, stack(0x00000079bc600000,0x00000079bc700000) (1024K)]
  0x0000019a23f8be40 JavaThread "Reference Handler"          daemon [_thread_blocked, id=15776, stack(0x00000079bce00000,0x00000079bcf00000) (1024K)]
  0x0000019a23f8ee10 JavaThread "Finalizer"                  daemon [_thread_blocked, id=9768, stack(0x00000079bcf00000,0x00000079bd000000) (1024K)]
  0x0000019a23f909d0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=2024, stack(0x00000079bd000000,0x00000079bd100000) (1024K)]
  0x0000019a23f91420 JavaThread "Attach Listener"            daemon [_thread_blocked, id=15100, stack(0x00000079bd100000,0x00000079bd200000) (1024K)]
  0x0000019a23f91e70 JavaThread "Service Thread"             daemon [_thread_blocked, id=5216, stack(0x00000079bd200000,0x00000079bd300000) (1024K)]
  0x0000019a23f928c0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=15272, stack(0x00000079bd300000,0x00000079bd400000) (1024K)]
  0x0000019a23f93640 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=14456, stack(0x00000079bd400000,0x00000079bd500000) (1024K)]
=>0x0000019a23faece0 JavaThread "C1 CompilerThread0"         daemon [_thread_in_vm, id=5228, stack(0x00000079bd500000,0x00000079bd600000) (1024K)]
  0x0000019a65205e70 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=8796, stack(0x00000079bd600000,0x00000079bd700000) (1024K)]
  0x0000019a6521baf0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=16288, stack(0x00000079bd700000,0x00000079bd800000) (1024K)]
  0x0000019a654f7570 JavaThread "mysql-cj-abandoned-connection-cleanup" daemon [_thread_blocked, id=9112, stack(0x00000079bd900000,0x00000079bda00000) (1024K)]
  0x0000019a655fe7a0 JavaThread "RMI TCP Accept-38707"       daemon [_thread_in_native, id=16068, stack(0x00000079bdc00000,0x00000079bdd00000) (1024K)]
  0x0000019a655faab0 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=2584, stack(0x00000079bdd00000,0x00000079bde00000) (1024K)]
  0x0000019a655fb110 JavaThread "RMI Reaper"                        [_thread_blocked, id=14988, stack(0x00000079bde00000,0x00000079bdf00000) (1024K)]
  0x0000019a655fb770 JavaThread "RMI GC Daemon"              daemon [_thread_blocked, id=10604, stack(0x00000079bdf00000,0x00000079be000000) (1024K)]
  0x0000019a656fcaf0 JavaThread "RMI TCP Connection(1)-127.0.0.1" daemon [_thread_in_native, id=15384, stack(0x00000079be000000,0x00000079be100000) (1024K)]
  0x0000019a65885f20 JavaThread "RMI Scheduler(0)"           daemon [_thread_blocked, id=3548, stack(0x00000079be300000,0x00000079be400000) (1024K)]
  0x0000019a65885890 JavaThread "RMI TCP Connection(4)-127.0.0.1" daemon [_thread_in_native, id=15288, stack(0x00000079be400000,0x00000079be500000) (1024K)]
  0x0000019a65885200 JavaThread "RMI RenewClean-[127.0.0.1:13392]" daemon [_thread_blocked, id=16216, stack(0x00000079be500000,0x00000079be600000) (1024K)]
Total: 20

Other Threads:
  0x0000019a23f6a9b0 VMThread "VM Thread"                           [id=7832, stack(0x00000079bcd00000,0x00000079bce00000) (1024K)]
  0x0000019a23f5b1b0 WatcherThread "VM Periodic Task Thread"        [id=14056, stack(0x00000079bcc00000,0x00000079bcd00000) (1024K)]
  0x0000019a0c2d03b0 WorkerThread "GC Thread#0"                     [id=9224, stack(0x00000079bc700000,0x00000079bc800000) (1024K)]
  0x0000019a657490c0 WorkerThread "GC Thread#1"                     [id=16196, stack(0x00000079bd800000,0x00000079bd900000) (1024K)]
  0x0000019a65749460 WorkerThread "GC Thread#2"                     [id=13444, stack(0x00000079bda00000,0x00000079bdb00000) (1024K)]
  0x0000019a6575e7f0 WorkerThread "GC Thread#3"                     [id=9488, stack(0x00000079bdb00000,0x00000079bdc00000) (1024K)]
  0x0000019a0c2e1eb0 ConcurrentGCThread "G1 Main Marker"            [id=14564, stack(0x00000079bc800000,0x00000079bc900000) (1024K)]
  0x0000019a0c2e3210 WorkerThread "G1 Conc#0"                       [id=14612, stack(0x00000079bc900000,0x00000079bca00000) (1024K)]
  0x0000019a23e214b0 ConcurrentGCThread "G1 Refine#0"               [id=12668, stack(0x00000079bca00000,0x00000079bcb00000) (1024K)]
  0x0000019a23e22880 ConcurrentGCThread "G1 Service"                [id=5016, stack(0x00000079bcb00000,0x00000079bcc00000) (1024K)]
Total: 10

Threads with active compile tasks:
C1 CompilerThread0  631402 2482   !   3       sun.rmi.transport.Transport::serviceCall (370 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000019a24000000-0x0000019a24d10000-0x0000019a24d10000), size 13697024, SharedBaseAddress: 0x0000019a24000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000019a25000000-0x0000019a65000000, reserved size: 1073741824
Narrow klass base: 0x0000019a24000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 4 total, 4 available
 Memory: 16337M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 128M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 131072K, used 25609K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 2 survivors (2048K)
 Metaspace       used 17368K, committed 17792K, reserved 1114112K
  class space    used 1981K, committed 2176K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080000000| PB 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080100000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HS|  |TAMS 0x0000000080200000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HS|  |TAMS 0x0000000080300000| PB 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080700000| PB 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080833f80, 0x0000000080900000| 20%| O|  |TAMS 0x0000000080800000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080900000, 0x0000000080a00000|  0%| F|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080a00000, 0x0000000080b00000|  0%| F|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080b00000, 0x0000000080c00000|  0%| F|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080c00000, 0x0000000080d00000|  0%| F|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080d00000, 0x0000000080e00000|  0%| F|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080e00000, 0x0000000080f00000|  0%| F|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000080f00000, 0x0000000081000000|  0%| F|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081000000, 0x0000000081100000|  0%| F|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081100000, 0x0000000081200000|  0%| F|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081200000, 0x0000000081300000|  0%| F|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081300000, 0x0000000081400000|  0%| F|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081400000, 0x0000000081500000|  0%| F|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081500000, 0x0000000081600000|  0%| F|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081600000, 0x0000000081700000|  0%| F|  |TAMS 0x0000000081600000| PB 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081700000, 0x0000000081800000|  0%| F|  |TAMS 0x0000000081700000| PB 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081800000, 0x0000000081900000|  0%| F|  |TAMS 0x0000000081800000| PB 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081900000, 0x0000000081a00000|  0%| F|  |TAMS 0x0000000081900000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081a00000, 0x0000000081b00000|  0%| F|  |TAMS 0x0000000081a00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081b00000, 0x0000000081c00000|  0%| F|  |TAMS 0x0000000081b00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081c00000, 0x0000000081d00000|  0%| F|  |TAMS 0x0000000081c00000| PB 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081d00000, 0x0000000081e00000|  0%| F|  |TAMS 0x0000000081d00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081e00000, 0x0000000081f00000|  0%| F|  |TAMS 0x0000000081e00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000081f00000, 0x0000000082000000|  0%| F|  |TAMS 0x0000000081f00000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082000000, 0x0000000082100000|  0%| F|  |TAMS 0x0000000082000000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082100000, 0x0000000082200000|  0%| F|  |TAMS 0x0000000082100000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082200000, 0x0000000082300000|  0%| F|  |TAMS 0x0000000082200000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082300000, 0x0000000082400000|  0%| F|  |TAMS 0x0000000082300000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082400000, 0x0000000082500000|  0%| F|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082500000, 0x0000000082600000|  0%| F|  |TAMS 0x0000000082500000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082600000, 0x0000000082700000|  0%| F|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082700000, 0x0000000082800000|  0%| F|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082800000, 0x0000000082900000|  0%| F|  |TAMS 0x0000000082800000| PB 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082900000, 0x0000000082a00000|  0%| F|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082a00000, 0x0000000082b00000|  0%| F|  |TAMS 0x0000000082a00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082b00000, 0x0000000082c00000|  0%| F|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082c00000, 0x0000000082d00000|  0%| F|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082d00000, 0x0000000082e00000|  0%| F|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082e00000, 0x0000000082f00000|  0%| F|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000082f00000, 0x0000000083000000|  0%| F|  |TAMS 0x0000000082f00000| PB 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083000000, 0x0000000083100000|  0%| F|  |TAMS 0x0000000083000000| PB 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083100000, 0x0000000083200000|  0%| F|  |TAMS 0x0000000083100000| PB 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083200000, 0x0000000083300000|  0%| F|  |TAMS 0x0000000083200000| PB 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083300000, 0x0000000083400000|  0%| F|  |TAMS 0x0000000083300000| PB 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083400000, 0x0000000083500000|  0%| F|  |TAMS 0x0000000083400000| PB 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083500000, 0x0000000083600000|  0%| F|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083600000, 0x0000000083700000|  0%| F|  |TAMS 0x0000000083600000| PB 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083700000, 0x0000000083800000|  0%| F|  |TAMS 0x0000000083700000| PB 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083800000, 0x0000000083900000|  0%| F|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083900000, 0x0000000083a00000|  0%| F|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083a00000, 0x0000000083b00000|  0%| F|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083b00000, 0x0000000083c00000|  0%| F|  |TAMS 0x0000000083b00000| PB 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083c00000, 0x0000000083d00000|  0%| F|  |TAMS 0x0000000083c00000| PB 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083d00000, 0x0000000083e00000|  0%| F|  |TAMS 0x0000000083d00000| PB 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083e00000, 0x0000000083f00000|  0%| F|  |TAMS 0x0000000083e00000| PB 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000083f00000, 0x0000000084000000|  0%| F|  |TAMS 0x0000000083f00000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084000000, 0x0000000084100000|  0%| F|  |TAMS 0x0000000084000000| PB 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084100000, 0x0000000084200000|  0%| F|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084200000, 0x0000000084300000|  0%| F|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084300000, 0x0000000084400000|  0%| F|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084400000, 0x0000000084500000|  0%| F|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084500000, 0x0000000084600000|  0%| F|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084600000, 0x0000000084700000|  0%| F|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084700000, 0x0000000084800000|  0%| F|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084800000, 0x0000000084900000|  0%| F|  |TAMS 0x0000000084800000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084900000, 0x0000000084a00000|  0%| F|  |TAMS 0x0000000084900000| PB 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084a00000, 0x0000000084b00000|  0%| F|  |TAMS 0x0000000084a00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084b00000, 0x0000000084c00000|  0%| F|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084c00000, 0x0000000084d00000|  0%| F|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084d00000, 0x0000000084e00000|  0%| F|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084e00000, 0x0000000084f00000|  0%| F|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000084f00000, 0x0000000085000000|  0%| F|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085000000, 0x0000000085100000|  0%| F|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085100000, 0x0000000085200000|  0%| F|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085200000, 0x0000000085300000|  0%| F|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085300000, 0x0000000085400000|  0%| F|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085400000, 0x0000000085500000|  0%| F|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085500000, 0x0000000085600000|  0%| F|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085600000, 0x0000000085700000|  0%| F|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085700000, 0x0000000085800000|  0%| F|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085800000, 0x0000000085900000|  0%| F|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085900000, 0x0000000085a00000|  0%| F|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085a00000, 0x0000000085b00000|  0%| F|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085b00000, 0x0000000085c00000|  0%| F|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085c00000, 0x0000000085d00000|  0%| F|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085d00000, 0x0000000085e00000|  0%| F|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085e00000, 0x0000000085f00000|  0%| F|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000085f00000, 0x0000000086000000|  0%| F|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086000000, 0x0000000086100000|  0%| F|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086100000, 0x0000000086200000|  0%| F|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086200000, 0x0000000086300000|  0%| F|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086300000, 0x0000000086400000|  0%| F|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086400000, 0x0000000086500000|  0%| F|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086500000, 0x0000000086600000|  0%| F|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086600000, 0x0000000086700000|  0%| F|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086700000, 0x0000000086800000|  0%| F|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086800000, 0x0000000086900000|  0%| F|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086900000, 0x0000000086a00000|  0%| F|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086a00000, 0x0000000086b00000|  0%| F|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086b00000, 0x0000000086c00000|  0%| F|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086c00000, 0x0000000086d00000|  0%| F|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086d8a240, 0x0000000086e00000| 53%| E|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Complete 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| E|CS|TAMS 0x0000000086e00000| PB 0x0000000086e00000| Complete 
| 111|0x0000000086f00000, 0x0000000086fce860, 0x0000000087000000| 80%| S|CS|TAMS 0x0000000086f00000| PB 0x0000000086f00000| Complete 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| S|CS|TAMS 0x0000000087000000| PB 0x0000000087000000| Complete 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| E|CS|TAMS 0x0000000087100000| PB 0x0000000087100000| Complete 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| E|CS|TAMS 0x0000000087200000| PB 0x0000000087200000| Complete 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| E|CS|TAMS 0x0000000087300000| PB 0x0000000087300000| Complete 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| E|CS|TAMS 0x0000000087400000| PB 0x0000000087400000| Complete 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| E|CS|TAMS 0x0000000087500000| PB 0x0000000087500000| Complete 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| E|CS|TAMS 0x0000000087600000| PB 0x0000000087600000| Complete 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| E|CS|TAMS 0x0000000087700000| PB 0x0000000087700000| Complete 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| E|CS|TAMS 0x0000000087800000| PB 0x0000000087800000| Complete 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| E|CS|TAMS 0x0000000087900000| PB 0x0000000087900000| Complete 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| E|CS|TAMS 0x0000000087a00000| PB 0x0000000087a00000| Complete 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| E|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Complete 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| E|CS|TAMS 0x0000000087c00000| PB 0x0000000087c00000| Complete 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| E|CS|TAMS 0x0000000087d00000| PB 0x0000000087d00000| Complete 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| E|CS|TAMS 0x0000000087e00000| PB 0x0000000087e00000| Complete 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| E|CS|TAMS 0x0000000087f00000| PB 0x0000000087f00000| Complete 

Card table byte_map: [0x0000019a1f6d0000,0x0000019a1fad0000] _byte_map_base: 0x0000019a1f2d0000

Marking Bits: (CMBitMap*) 0x0000019a0c2d15d0
 Bits: [0x0000019a1fad0000, 0x0000019a21ad0000)

Polling page: 0x0000019a0ba50000

Metaspace:

Usage:
  Non-class:     15.03 MB used.
      Class:      1.93 MB used.
       Both:     16.96 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      15.25 MB ( 24%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       2.12 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      17.38 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  212.00 KB
       Class:  13.92 MB
        Both:  14.12 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 496.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 278.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 941.
num_chunk_merges: 0.
num_chunk_splits: 573.
num_chunks_enlarged: 267.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=939Kb max_used=939Kb free=119060Kb
 bounds [0x0000019a17da0000, 0x0000019a18010000, 0x0000019a1f2d0000]
CodeHeap 'profiled nmethods': size=120000Kb used=4742Kb max_used=4742Kb free=115257Kb
 bounds [0x0000019a102d0000, 0x0000019a10780000, 0x0000019a17800000]
CodeHeap 'non-nmethods': size=5760Kb used=1521Kb max_used=1604Kb free=4238Kb
 bounds [0x0000019a17800000, 0x0000019a17a70000, 0x0000019a17da0000]
 total_blobs=3069 nmethods=2481 adapters=492
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 605.574 Thread 0x0000019a23faece0 nmethod 2473 0x0000019a1076ca10 code [0x0000019a1076cbc0, 0x0000019a1076cda8]
Event: 605.574 Thread 0x0000019a23faece0 2472       3       java.lang.ClassLoader::getResource (41 bytes)
Event: 605.574 Thread 0x0000019a23faece0 nmethod 2472 0x0000019a1076ce90 code [0x0000019a1076d0a0, 0x0000019a1076d690]
Event: 622.367 Thread 0x0000019a23faece0 2474       3       java.io.ObjectInputStream$PeekInputStream::<init> (20 bytes)
Event: 622.367 Thread 0x0000019a23faece0 nmethod 2474 0x0000019a1076d890 code [0x0000019a1076da40, 0x0000019a1076dc78]
Event: 622.367 Thread 0x0000019a23faece0 2475       3       sun.rmi.server.MarshalOutputStream::<init> (7 bytes)
Event: 622.367 Thread 0x0000019a23faece0 nmethod 2475 0x0000019a1076dd90 code [0x0000019a1076dfc0, 0x0000019a1076e790]
Event: 622.367 Thread 0x0000019a23faece0 2476       3       sun.rmi.server.MarshalOutputStream::<init> (23 bytes)
Event: 622.368 Thread 0x0000019a23faece0 nmethod 2476 0x0000019a1076ea90 code [0x0000019a1076ecc0, 0x0000019a1076f440]
Event: 622.368 Thread 0x0000019a23faece0 2477       3       java.io.ObjectOutputStream::<init> (99 bytes)
Event: 622.368 Thread 0x0000019a23faece0 nmethod 2477 0x0000019a1076f710 code [0x0000019a1076f980, 0x0000019a10770180]
Event: 622.368 Thread 0x0000019a23faece0 2478       3       sun.rmi.transport.Target::getImpl (11 bytes)
Event: 622.368 Thread 0x0000019a23faece0 nmethod 2478 0x0000019a10770490 code [0x0000019a10770640, 0x0000019a10770920]
Event: 631.368 Thread 0x0000019a23faece0 2479       3       java.rmi.server.ObjID::read (22 bytes)
Event: 631.368 Thread 0x0000019a23faece0 nmethod 2479 0x0000019a10770a10 code [0x0000019a10770c00, 0x0000019a10771010]
Event: 631.368 Thread 0x0000019a23faece0 2480       3       java.rmi.server.ObjID::<init> (15 bytes)
Event: 631.369 Thread 0x0000019a23faece0 nmethod 2480 0x0000019a10771210 code [0x0000019a107713c0, 0x0000019a10771580]
Event: 631.369 Thread 0x0000019a23faece0 2481       3       sun.rmi.transport.StreamRemoteCall::<init> (35 bytes)
Event: 631.369 Thread 0x0000019a23faece0 nmethod 2481 0x0000019a10771610 code [0x0000019a107717c0, 0x0000019a10771a48]
Event: 631.369 Thread 0x0000019a23faece0 2482   !   3       sun.rmi.transport.Transport::serviceCall (370 bytes)

GC Heap History (4 events):
Event: 0.796 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 131072K, used 17408K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 0 survivors (0K)
 Metaspace       used 3713K, committed 3968K, reserved 1114112K
  class space    used 470K, committed 576K, reserved 1048576K
}
Event: 0.859 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 131072K, used 8362K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 3713K, committed 3968K, reserved 1114112K
  class space    used 470K, committed 576K, reserved 1048576K
}
Event: 1.491 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 131072K, used 20650K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 2 survivors (2048K)
 Metaspace       used 10257K, committed 10624K, reserved 1114112K
  class space    used 1229K, committed 1408K, reserved 1048576K
}
Event: 1.494 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 131072K, used 10249K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 10257K, committed 10624K, reserved 1114112K
  class space    used 1229K, committed 1408K, reserved 1048576K
}

Dll operation events (2 events):
Event: 0.009 Loaded shared library D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\java.dll
Event: 0.056 Loaded shared library D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\zip.dll

Deoptimization events (20 events):
Event: 1.902 Thread 0x0000019a65885890 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000019a17e47a80 relative=0x0000000000000520
Event: 1.902 Thread 0x0000019a65885890 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000019a17e47a80 method=java.util.regex.Pattern$SliceI.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 92 c2
Event: 1.902 Thread 0x0000019a65885890 DEOPT PACKING pc=0x0000019a17e47a80 sp=0x00000079be4fe250
Event: 1.902 Thread 0x0000019a65885890 DEOPT UNPACKING pc=0x0000019a178546a2 sp=0x00000079be4fe1e8 mode 2
Event: 1.976 Thread 0x0000019a65885890 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000019a17e56e00 relative=0x0000000000000780
Event: 1.976 Thread 0x0000019a65885890 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000019a17e56e00 method=java.lang.AbstractStringBuilder.append(Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; @ 1 c2
Event: 1.976 Thread 0x0000019a65885890 DEOPT PACKING pc=0x0000019a17e56e00 sp=0x00000079be4fe440
Event: 1.976 Thread 0x0000019a65885890 DEOPT UNPACKING pc=0x0000019a178546a2 sp=0x00000079be4fe3d0 mode 2
Event: 2.028 Thread 0x0000019a65885890 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000019a17e5b878 relative=0x0000000000000338
Event: 2.028 Thread 0x0000019a65885890 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000019a17e5b878 method=java.util.regex.Matcher.search(I)Z @ 76 c2
Event: 2.028 Thread 0x0000019a65885890 DEOPT PACKING pc=0x0000019a17e5b878 sp=0x00000079be4fe310
Event: 2.028 Thread 0x0000019a65885890 DEOPT UNPACKING pc=0x0000019a178546a2 sp=0x00000079be4fe2b0 mode 2
Event: 2.028 Thread 0x0000019a65885890 Uncommon trap: trap_request=0xffffff76 fr.pc=0x0000019a17e5ab64 relative=0x00000000000002a4
Event: 2.028 Thread 0x0000019a65885890 Uncommon trap: reason=predicate action=maybe_recompile pc=0x0000019a17e5ab64 method=java.util.regex.Pattern$BmpCharPropertyGreedy.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 12 c2
Event: 2.028 Thread 0x0000019a65885890 DEOPT PACKING pc=0x0000019a17e5ab64 sp=0x00000079be4fe1c0
Event: 2.028 Thread 0x0000019a65885890 DEOPT UNPACKING pc=0x0000019a178546a2 sp=0x00000079be4fe148 mode 2
Event: 2.059 Thread 0x0000019a65885890 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000019a17e066d8 relative=0x0000000000002238
Event: 2.059 Thread 0x0000019a65885890 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000019a17e066d8 method=jdk.internal.org.objectweb.asm.Frame.execute(IILjdk/internal/org/objectweb/asm/Symbol;Ljdk/internal/org/objectweb/asm/SymbolTable;)V @ 1 c2
Event: 2.059 Thread 0x0000019a65885890 DEOPT PACKING pc=0x0000019a17e066d8 sp=0x00000079be4fdca0
Event: 2.059 Thread 0x0000019a65885890 DEOPT UNPACKING pc=0x0000019a178546a2 sp=0x00000079be4fdc30 mode 2

Classes loaded (20 events):
Event: 1.793 Loading class sun/security/ssl/NewSessionTicket$NewSessionTicketMessage done
Event: 1.793 Loading class sun/security/ssl/NewSessionTicket$T13NewSessionTicketMessage done
Event: 1.907 Loading class java/util/TimerTask
Event: 1.907 Loading class java/util/TimerTask done
Event: 1.974 Loading class java/time/format/DateTimeParseException
Event: 1.974 Loading class java/time/DateTimeException
Event: 1.974 Loading class java/time/DateTimeException done
Event: 1.974 Loading class java/time/format/DateTimeParseException done
Event: 2.042 Loading class java/nio/charset/CharacterCodingException
Event: 2.042 Loading class java/nio/charset/CharacterCodingException done
Event: 2.081 Loading class sun/invoke/util/ValueConversions$1
Event: 2.081 Loading class sun/invoke/util/ValueConversions$1 done
Event: 2.082 Loading class java/lang/invoke/BoundMethodHandle$Species_LI
Event: 2.082 Loading class java/lang/invoke/BoundMethodHandle$Species_LI done
Event: 2.128 Loading class java/util/AbstractList$ListItr
Event: 2.128 Loading class java/util/AbstractList$ListItr done
Event: 2.184 Loading class java/util/function/UnaryOperator
Event: 2.184 Loading class java/util/function/UnaryOperator done
Event: 16.217 Loading class java/util/ArrayList$ListItr
Event: 16.217 Loading class java/util/ArrayList$ListItr done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 1.227 Thread 0x0000019a65885200 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087b5c810}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, long)'> (0x0000000087b5c810) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.227 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087bc5c88}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000087bc5c88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.326 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x000000008771a4b0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008771a4b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.327 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087723810}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000087723810) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.548 Thread 0x0000019a65885890 Exception <a 'sun/nio/fs/WindowsException'{0x0000000087dc6040}> (0x0000000087dc6040) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 1.723 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x00000000879bfc40}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000000879bfc40) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.976 Thread 0x0000019a65885890 Implicit null exception at 0x0000019a17e566c4 to 0x0000019a17e56de8
Event: 1.976 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x00000000876c3ab0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x00000000876c3ab0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 2.038 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087583010}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000087583010) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 2.040 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x000000008758f610}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008758f610) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 2.040 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087593710}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000087593710) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 2.040 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x00000000875a1b58}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000875a1b58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 2.045 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x00000000875b8ce8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000000875b8ce8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 2.081 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087343948}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x0000000087343948) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 2.081 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087347ab0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000087347ab0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 2.082 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087359d50}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000087359d50) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 2.133 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087385a98}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x0000000087385a98) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 2.134 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087389920}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, int)'> (0x0000000087389920) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 2.281 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x000000008716c698}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x000000008716c698) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 2.291 Thread 0x0000019a65885890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000087195c20}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x0000000087195c20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 218.234 Executing VM operation: Cleanup
Event: 218.234 Executing VM operation: Cleanup done
Event: 236.243 Executing VM operation: Cleanup
Event: 236.243 Executing VM operation: Cleanup done
Event: 301.274 Executing VM operation: Cleanup
Event: 301.274 Executing VM operation: Cleanup done
Event: 406.328 Executing VM operation: Cleanup
Event: 406.328 Executing VM operation: Cleanup done
Event: 436.346 Executing VM operation: Cleanup
Event: 436.346 Executing VM operation: Cleanup done
Event: 487.374 Executing VM operation: Cleanup
Event: 487.374 Executing VM operation: Cleanup done
Event: 514.387 Executing VM operation: Cleanup
Event: 514.387 Executing VM operation: Cleanup done
Event: 532.396 Executing VM operation: Cleanup
Event: 532.396 Executing VM operation: Cleanup done
Event: 559.409 Executing VM operation: Cleanup
Event: 559.409 Executing VM operation: Cleanup done
Event: 611.434 Executing VM operation: Cleanup
Event: 611.434 Executing VM operation: Cleanup done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 1.210 Thread 0x0000019a65885890 Thread added: 0x0000019a65885200
Event: 1.309 Thread 0x0000019a23faece0 Thread added: 0x0000019a654d56d0
Event: 1.522 Loaded shared library D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\sunmscapi.dll
Event: 2.183 Thread 0x0000019a654d56d0 Thread exited: 0x0000019a654d56d0
Event: 2.257 Thread 0x0000019a23faece0 Thread added: 0x0000019a65d0d7b0
Event: 7.493 Thread 0x0000019a65d0d7b0 Thread exited: 0x0000019a65d0d7b0
Event: 30.921 Thread 0x0000019a65885890 Thread added: 0x0000019a658865b0
Event: 30.921 Thread 0x0000019a65885890 Thread added: 0x0000019a65886c40
Event: 30.922 Thread 0x0000019a65885890 Thread added: 0x0000019a658837c0
Event: 30.922 Thread 0x0000019a65885890 Thread added: 0x0000019a65883e50
Event: 30.922 Thread 0x0000019a65885890 Thread added: 0x0000019a658844e0
Event: 30.922 Thread 0x0000019a65885890 Thread added: 0x0000019a65884b70
Event: 30.923 Thread 0x0000019a65886c40 Thread exited: 0x0000019a65886c40
Event: 30.923 Thread 0x0000019a658865b0 Thread exited: 0x0000019a658865b0
Event: 30.923 Thread 0x0000019a658837c0 Thread exited: 0x0000019a658837c0
Event: 30.923 Thread 0x0000019a65883e50 Thread exited: 0x0000019a65883e50
Event: 30.924 Thread 0x0000019a65884b70 Thread exited: 0x0000019a65884b70
Event: 30.924 Thread 0x0000019a658844e0 Thread exited: 0x0000019a658844e0
Event: 76.194 Thread 0x0000019a656fd150 Thread exited: 0x0000019a656fd150
Event: 91.204 Thread 0x0000019a65887b90 Thread exited: 0x0000019a65887b90


Dynamic libraries:
0x00007ff63fae0000 - 0x00007ff63faea000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\java.exe
0x00007ffb51d90000 - 0x00007ffb51f88000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb51630000 - 0x00007ffb516ed000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb4faf0000 - 0x00007ffb4fde6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb4f4a0000 - 0x00007ffb4f5a0000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb4b360000 - 0x00007ffb4b37b000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\VCRUNTIME140.dll
0x00007ffb48f10000 - 0x00007ffb48f28000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\jli.dll
0x00007ffb51850000 - 0x00007ffb519ee000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb4fac0000 - 0x00007ffb4fae2000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb505f0000 - 0x00007ffb5061c000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb4f7f0000 - 0x00007ffb4f90a000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb4f750000 - 0x00007ffb4f7ed000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb3d200000 - 0x00007ffb3d49a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.3636_none_60b6a03d71f818d5\COMCTL32.dll
0x00007ffb51480000 - 0x00007ffb5151e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb513f0000 - 0x00007ffb51420000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb4b340000 - 0x00007ffb4b34c000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\vcruntime140_1.dll
0x00007ffb22a40000 - 0x00007ffb22acd000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\msvcp140.dll
0x00007ffb1add0000 - 0x00007ffb1bb91000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\server\jvm.dll
0x00007ffb517a0000 - 0x00007ffb5184f000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb51700000 - 0x00007ffb5179c000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb50200000 - 0x00007ffb50326000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb50580000 - 0x00007ffb505eb000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb4e9d0000 - 0x00007ffb4ea1b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb45540000 - 0x00007ffb45567000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb45570000 - 0x00007ffb4557a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb4e890000 - 0x00007ffb4e8a2000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb4d350000 - 0x00007ffb4d362000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb4b280000 - 0x00007ffb4b28a000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\jimage.dll
0x00007ffb46bc0000 - 0x00007ffb46da4000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb31660000 - 0x00007ffb31694000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb4fa30000 - 0x00007ffb4fab2000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb48ef0000 - 0x00007ffb48f10000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\java.dll
0x00007ffb50ca0000 - 0x00007ffb513e5000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb4d550000 - 0x00007ffb4dceb000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb519f0000 - 0x00007ffb51d44000 	C:\WINDOWS\System32\combase.dll
0x00007ffb4eeb0000 - 0x00007ffb4eedd000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffb50450000 - 0x00007ffb504fd000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb4fdf0000 - 0x00007ffb4fe45000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffb4f3a0000 - 0x00007ffb4f3c5000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffb48ed0000 - 0x00007ffb48ee8000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\zip.dll
0x00007ffb4b250000 - 0x00007ffb4b260000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\net.dll
0x00007ffb467c0000 - 0x00007ffb468ca000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffb4ec10000 - 0x00007ffb4ec7a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffb48eb0000 - 0x00007ffb48ec6000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\nio.dll
0x00007ffb4e8b0000 - 0x00007ffb4e8eb000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb516f0000 - 0x00007ffb516f8000 	C:\WINDOWS\System32\NSI.dll
0x00007ffb4ee00000 - 0x00007ffb4ee18000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffb4e4d0000 - 0x00007ffb4e504000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffb4f470000 - 0x00007ffb4f497000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffb4f360000 - 0x00007ffb4f38e000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffb4ee20000 - 0x00007ffb4ee2c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb48e80000 - 0x00007ffb48e89000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\extnet.dll
0x00007ffb4a2e0000 - 0x00007ffb4a2e7000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\rmi.dll
0x00007ffb4e900000 - 0x00007ffb4e9ca000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffb3e910000 - 0x00007ffb3e91a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffb3f600000 - 0x00007ffb3f680000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffb48e90000 - 0x00007ffb48e9e000 	D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\sunmscapi.dll
0x00007ffb4f5a0000 - 0x00007ffb4f6fd000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffb4ef20000 - 0x00007ffb4ef47000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffb4eee0000 - 0x00007ffb4ef1b000 	C:\WINDOWS\SYSTEM32\NTASN1.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.3636_none_60b6a03d71f818d5;D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\jbr\bin\server

VM Arguments:
jvm_args: -Djava.rmi.server.hostname=127.0.0.1 -Duser.timezone=UTC -Xmx2048m -Xms128m -Dhttp.proxyHost=127.0.0.1 -Dhttp.proxyPort=10809 -Dhttps.proxyHost=127.0.0.1 -Dhttps.proxyPort=10809 -Dhttp.useProxy=true -Dhttp.nonProxyHosts=localhost|127.0.0.1|::1 -Dhttps.nonProxyHosts=localhost|127.0.0.1|::1 -DsocksNonProxyHosts=localhost|127.0.0.1|::1 -Djdbc.classpath=C:\Users\<USER>\AppData\Local\Temp\RemoteDriver5.classpath.txt --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.desktop/java.awt.peer=ALL-UNNAMED --add-opens=java.desktop/java.awt=ALL-UNNAMED --add-opens=jdk.unsupported/sun.misc=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 
java_command: com.intellij.database.remote.RemoteJdbcServer com.mysql.cj.jdbc.Driver
java_class_path (initial): D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\lib\util_rt.jar;D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\lib\util-8.jar;D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\lib\groovy.jar;D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\plugins\DatabaseTools\lib\jdbc-console.jar;D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\lib\modules\intellij.grid.types.jar;D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\lib\util.jar;D:\APP\IntelliJ-IDEA\ideaIU-2025.1.2.win\lib\bouncy-castle.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 134217728                                 {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 134217728                                 {product} {command line}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Environment\java_install\zulu17.54.21-ca-jdk17.0.13-win_x64
PATH=C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;D:\Environment\go_install\go\bin;D:\Environment\java_install\zulu17.54.21-ca-jdk17.0.13-win_x64\bin;D:\Environment\NVM\nvm;D:\Environment\NVM\nodejs;D:\Environment\NVM\nodejs\node_global;D:\Environment\git\git-install-lib\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\APP\bandzip\bandzip7.06\;D:\Environment\NVM\nvm;D:\Environment\NVM\nodejs
USERNAME=shaun
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 10, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 17, weak refs: 0

JNI global refs memory usage: 835, weak refs: 201

Process memory usage:
Resident Set Size: 102160K (0% of 16729296K total physical memory with 1014596K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 10840K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 3003K
Loader com.intellij.database.remote.jdbc.impl.JdbcClassLoader$JdbcClassLoaderImpl      : 2666K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 770K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 81984B
Loader com.intellij.database.remote.jdbc.impl.JdbcClassLoader$1                        : 7728B


[error occurred during error reporting (Classloader stats), id 0xe0000001, Out of Memory Error (s\src\hotspot\share\memory\arena.cpp:168)]
---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.3636)
OS uptime: 0 days 15:17 hours

CPU: total 4 (initial active 4) (4 cores per cpu, 1 threads per core) family 6 model 158 stepping 10 microcode 0xb4, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 4 processors :
  Max Mhz: 3600, Current Mhz: 3600, Mhz Limit: 3600

Memory: 4k page, system-wide physical 16337M (990M free)
TotalPageFile size 26490M (AvailPageFile size 1M)
current process WorkingSet (physical memory assigned to process): 99M, peak: 120M
current process commit charge ("private bytes"): 229M, peak: 253M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130) for windows-amd64 JRE (21.0.7+9-b895.130), built on 2025-05-13 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
