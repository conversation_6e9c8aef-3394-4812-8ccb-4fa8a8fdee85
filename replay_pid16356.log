version 2
JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 1
JvmtiExport can_post_on_exceptions 0
# 324 ciObject found
instanceKlass sun/rmi/transport/Transport
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
instanceKlass java/util/IdentityHashMap$IdentityHashMapIterator
# instanceKlass sun/rmi/transport/Target$$Lambda+0x0000019a251bba00
instanceKlass  @bci sun/rmi/transport/Target lambda$refSetRemove$1 (Ljava/rmi/server/Unreferenced;)V 11 <appendix> member <vmtarget> ; # sun/rmi/transport/Target$$Lambda+0x0000019a251bb7c8
instanceKlass  @bci sun/rmi/transport/Target refSetRemove (Ljava/rmi/dgc/VMID;)V 82 <appendix> member <vmtarget> ; # sun/rmi/transport/Target$$Lambda+0x0000019a251bb590
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25214c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25214800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25214400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25214000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25213c00
instanceKlass com/mysql/cj/jdbc/result/ResultSetMetaData$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25213800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25213400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25213000
instanceKlass java/sql/Ref
instanceKlass com/intellij/database/remote/jdbc/RemoteClob
instanceKlass com/intellij/database/remote/jdbc/RemoteBlob
instanceKlass com/intellij/database/remote/jdbc/RemoteResultSetMetaData
instanceKlass java/sql/Array
instanceKlass com/intellij/database/remote/jdbc/RemoteArray
instanceKlass com/intellij/database/remote/jdbc/RemoteRef
instanceKlass java/util/function/UnaryOperator
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25211c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25211800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25211400
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x0000019a25211000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25210c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25210800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25210400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25210000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2520bc00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a2520b800
instanceKlass java/lang/invoke/ClassSpecializer$Factory$1Var
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a2520b400
instanceKlass sun/invoke/util/ValueConversions$1
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a2520b000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a2520a800
instanceKlass com/intellij/database/remote/jdbc/RemoteResultSet
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2520a400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a2520a000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25209800
instanceKlass  @bci jdk/internal/reflect/DirectMethodHandleAccessor invokeImpl (Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; 72 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25209400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25209000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25208c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25208800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25208400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25208000
instanceKlass com/mysql/cj/jdbc/CommentClientInfoProvider
instanceKlass com/mysql/cj/jdbc/ClientInfoProvider
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25204c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25204800
instanceKlass com/intellij/database/remote/jdba/RemoteFacade
instanceKlass com/intellij/database/remote/jdbc/RemoteCallableStatement
instanceKlass com/intellij/database/remote/jdbc/RemoteDatabaseMetaData
instanceKlass com/intellij/database/remote/jdbc/impl/RemoteBatchPreparedStatementImpl$Configuration
instanceKlass com/intellij/database/remote/jdbc/RemoteSavepoint
instanceKlass com/intellij/execution/rmi/CastableArgument
instanceKlass com/intellij/database/remote/jdbc/RemoteBatchPreparedStatement
instanceKlass  @bci com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl computeJdbcCatalogIsSchema ()Z 63 <appendix> member <vmtarget> ; # com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl$$Lambda+0x0000019a25116048
instanceKlass  @bci com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl computeJdbcCatalogIsSchema ()Z 42 <appendix> member <vmtarget> ; # com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl$$Lambda+0x0000019a25115e10
instanceKlass  @bci com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl computeJdbcCatalogIsSchema ()Z 18 <appendix> member <vmtarget> ; # com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl$$Lambda+0x0000019a25115bd8
instanceKlass com/intellij/openapi/util/ThrowableComputable
instanceKlass  @bci com/intellij/database/remote/jdbc/helpers/JdbcNativeUtil getString (Ljava/sql/Connection;Ljava/lang/String;I)Ljava/lang/String; 26 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a25204000
instanceKlass  @bci com/intellij/database/remote/jdbc/helpers/JdbcNativeUtil getString (Ljava/sql/Connection;Ljava/lang/String;I)Ljava/lang/String; 26 <appendix> member <vmtarget> ; # com/intellij/database/remote/jdbc/helpers/JdbcNativeUtil$$Lambda+0x0000019a25115790
instanceKlass com/mysql/cj/jdbc/result/ResultSetMetaData
instanceKlass sun/util/resources/Bundles$CacheKeyReference
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper newResourceBundle (Ljava/lang/Class;)Ljava/util/ResourceBundle; 22 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x800000010
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter applyAliases (Ljava/util/Locale;)Ljava/util/Locale; 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x80000005f
instanceKlass sun/util/resources/Bundles$CacheKey
instanceKlass sun/util/resources/LocaleData$LocaleDataStrategy
instanceKlass sun/util/resources/Bundles$Strategy
instanceKlass sun/util/resources/LocaleData$1
instanceKlass sun/util/resources/LocaleData
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 16 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x800000069
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 6 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x800000068
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter getCalendarDataProvider ()Ljava/util/spi/CalendarDataProvider; 8 <appendix> member <vmtarget> ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000062
instanceKlass sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool
instanceKlass java/util/Locale$Builder
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass java/util/Calendar$Builder
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getCalendarProvider ()Lsun/util/spi/CalendarProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000063
instanceKlass sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter <init> ()V 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000061
instanceKlass sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
instanceKlass sun/util/locale/InternalLocaleBuilder
instanceKlass sun/util/locale/StringTokenIterator
instanceKlass sun/util/locale/ParseStatus
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass com/mysql/cj/result/DefaultValueFactory
instanceKlass  @bci com/mysql/cj/jdbc/ClientPreparedStatement initializeFromQueryInfo ()V 52 <appendix> argL0 ; # com/mysql/cj/jdbc/ClientPreparedStatement$$Lambda+0x0000019a251ad2b8
instanceKlass com/mysql/cj/NativeQueryBindValue
instanceKlass com/mysql/cj/BindValue
instanceKlass com/mysql/cj/NativeQueryBindings
instanceKlass com/mysql/cj/QueryInfo
instanceKlass com/mysql/cj/NativeQueryAttributesBindings
instanceKlass com/mysql/cj/CancelQueryTask
instanceKlass java/util/TimerTask
instanceKlass com/mysql/cj/QueryAttributesBindings
instanceKlass com/mysql/cj/AbstractQuery
instanceKlass com/mysql/cj/PreparedQuery
instanceKlass com/mysql/cj/QueryBindings
instanceKlass com/mysql/cj/jdbc/ParameterBindings
instanceKlass java/sql/ParameterMetaData
instanceKlass com/mysql/cj/jdbc/EscapeProcessor
instanceKlass  @bci com/intellij/database/remote/jdbc/helpers/JdbcNativeUtil consume (Ljava/sql/Connection;Ljava/lang/String;Lcom/intellij/util/ThrowableConsumer;)V 30 <appendix> member <vmtarget> ; # com/intellij/database/remote/jdbc/helpers/JdbcNativeUtil$$Lambda+0x0000019a25115558
instanceKlass  @bci com/intellij/database/remote/jdbc/helpers/JdbcNativeUtil getStrings (Ljava/sql/Connection;Ljava/lang/String;)[Ljava/lang/String; 25 <appendix> member <vmtarget> ; # com/intellij/database/remote/jdbc/helpers/JdbcNativeUtil$$Lambda+0x0000019a25115320
instanceKlass com/intellij/util/ThrowableConsumer
instanceKlass com/intellij/openapi/util/Ref
instanceKlass com/intellij/openapi/util/text/StringUtilRt
instanceKlass com/mysql/cj/protocol/NetworkResources
instanceKlass  @bci com/mysql/cj/jdbc/ConnectionImpl unSafeQueryInterceptors ()V 37 <appendix> argL0 ; # com/mysql/cj/jdbc/ConnectionImpl$$Lambda+0x0000019a251a95c8
instanceKlass  @bci com/mysql/cj/jdbc/ConnectionImpl unSafeQueryInterceptors ()V 27 <appendix> argL0 ; # com/mysql/cj/jdbc/ConnectionImpl$$Lambda+0x0000019a251a9378
instanceKlass  @bci com/mysql/cj/jdbc/ConnectionImpl unSafeQueryInterceptors ()V 17 <appendix> member <vmtarget> ; # com/mysql/cj/jdbc/ConnectionImpl$$Lambda+0x0000019a251a9120
instanceKlass com/mysql/cj/LicenseConfiguration
instanceKlass com/mysql/cj/protocol/InternalDate
instanceKlass com/mysql/cj/result/StringValueFactory
instanceKlass com/mysql/cj/protocol/a/result/AbstractResultsetRows
instanceKlass com/mysql/cj/protocol/result/AbstractResultsetRow
instanceKlass com/mysql/cj/result/DefaultColumnDefinition$1
instanceKlass com/mysql/cj/protocol/a/MysqlTextValueDecoder
instanceKlass com/mysql/cj/protocol/ValueDecoder
instanceKlass com/mysql/cj/protocol/a/AbstractRowFactory
instanceKlass com/mysql/cj/result/DefaultColumnDefinition
instanceKlass com/mysql/cj/result/Field$1
instanceKlass com/mysql/cj/protocol/a/ColumnDefinitionReader$1
instanceKlass java/sql/SQLType
instanceKlass com/mysql/cj/util/LazyString
instanceKlass com/mysql/cj/result/Field
instanceKlass com/mysql/cj/protocol/a/ColumnDefinitionFactory
instanceKlass com/mysql/cj/protocol/a/ResultsetFactory
instanceKlass com/mysql/cj/protocol/a/NativeMessageBuilder
instanceKlass com/mysql/cj/protocol/a/MultiPacketReader
instanceKlass com/mysql/cj/protocol/a/TimeTrackingPacketReader
instanceKlass com/mysql/cj/protocol/a/TimeTrackingPacketSender
instanceKlass com/mysql/cj/protocol/a/NativeServerSessionStateController$NativeServerSessionStateChanges
instanceKlass com/mysql/cj/protocol/a/result/OkPacket
instanceKlass com/mysql/cj/protocol/Security
instanceKlass com/mysql/cj/protocol/a/authentication/AuthenticationWebAuthnClient
instanceKlass com/mysql/cj/protocol/a/authentication/AuthenticationFidoClient
instanceKlass com/mysql/cj/protocol/a/authentication/AuthenticationOciClient
instanceKlass  @bci com/mysql/cj/protocol/a/authentication/AuthenticationKerberosClient <init> ()V 43 <appendix> member <vmtarget> ; # com/mysql/cj/protocol/a/authentication/AuthenticationKerberosClient$$Lambda+0x0000019a251a5b78
instanceKlass com/mysql/cj/protocol/a/authentication/AuthenticationKerberosClient
instanceKlass  @bci com/mysql/cj/protocol/a/authentication/AuthenticationLdapSaslClientPlugin <init> ()V 46 <appendix> member <vmtarget> ; # com/mysql/cj/protocol/a/authentication/AuthenticationLdapSaslClientPlugin$$Lambda+0x0000019a251a5418
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass javax/security/auth/callback/Callback
instanceKlass javax/security/auth/login/Configuration
instanceKlass com/mysql/cj/protocol/a/authentication/AuthenticationLdapSaslClientPlugin
instanceKlass com/mysql/cj/protocol/a/authentication/MysqlOldPasswordPlugin
instanceKlass com/mysql/cj/protocol/a/authentication/Sha256PasswordPlugin
instanceKlass com/mysql/cj/protocol/a/authentication/MysqlClearPasswordPlugin
instanceKlass com/mysql/cj/protocol/a/authentication/MysqlNativePasswordPlugin
instanceKlass com/mysql/cj/protocol/AuthenticationPlugin
instanceKlass sun/security/ssl/X509Authentication$X509Possession
instanceKlass com/sun/crypto/provider/GaloisCounterMode$EncryptOp
instanceKlass sun/security/ssl/SSLAuthentication
instanceKlass  @bci sun/security/ssl/CertificateMessage$T13CertificateProducer choosePossession (Lsun/security/ssl/HandshakeContext;Lsun/security/ssl/ClientHello$ClientHelloMessage;)Lsun/security/ssl/SSLPossession; 89 <appendix> argL0 ; # sun/security/ssl/CertificateMessage$T13CertificateProducer$$Lambda+0x0000019a251b6680
instanceKlass  @bci sun/security/ssl/CertificateMessage$T13CertificateProducer choosePossession (Lsun/security/ssl/HandshakeContext;Lsun/security/ssl/ClientHello$ClientHelloMessage;)Lsun/security/ssl/SSLPossession; 79 <appendix> argL0 ; # sun/security/ssl/CertificateMessage$T13CertificateProducer$$Lambda+0x0000019a251b6420
instanceKlass  @bci sun/security/ssl/CertificateMessage$T13CertificateProducer choosePossession (Lsun/security/ssl/HandshakeContext;Lsun/security/ssl/ClientHello$ClientHelloMessage;)Lsun/security/ssl/SSLPossession; 69 <appendix> member <vmtarget> ; # sun/security/ssl/CertificateMessage$T13CertificateProducer$$Lambda+0x0000019a251b61b8
instanceKlass java/util/stream/DistinctOps
instanceKlass  @bci sun/security/ssl/CertificateMessage$T13CertificateProducer choosePossession (Lsun/security/ssl/HandshakeContext;Lsun/security/ssl/ClientHello$ClientHelloMessage;)Lsun/security/ssl/SSLPossession; 53 <appendix> argL0 ; # sun/security/ssl/CertificateMessage$T13CertificateProducer$$Lambda+0x0000019a251b5320
instanceKlass sun/security/ssl/SSLBasicKeyDerivation$SecretSizeSpec
instanceKlass sun/security/ssl/SSLBasicKeyDerivation
instanceKlass sun/security/ssl/Finished$1
instanceKlass sun/security/ssl/Finished$T13VerifyDataGenerator
instanceKlass sun/security/ssl/Finished$T12VerifyDataGenerator
instanceKlass sun/security/ssl/Finished$T10VerifyDataGenerator
instanceKlass sun/security/ssl/Finished$S30VerifyDataGenerator
instanceKlass sun/security/ssl/Finished$VerifyDataGenerator
instanceKlass sun/security/rsa/MGF1
instanceKlass sun/security/rsa/RSACore
instanceKlass java/security/interfaces/RSAPrivateKey
instanceKlass sun/security/util/SignatureUtil
instanceKlass sun/security/ssl/X509Authentication$X509Credentials
instanceKlass java/security/interfaces/DSAPublicKey
instanceKlass java/security/interfaces/DSAKey
instanceKlass sun/security/provider/certpath/PKIX
instanceKlass sun/security/ssl/SSLAlgorithmConstraints$SupportedSignatureAlgorithmConstraints
instanceKlass sun/security/ssl/SSLConfiguration$1
instanceKlass javax/net/ssl/SSLParameters
instanceKlass javax/net/ssl/SSLEngine
instanceKlass jdk/internal/event/EventHelper$ThreadTrackHolder
instanceKlass java/util/jar/JarFile$ThreadTrackHolder
instanceKlass jdk/internal/event/EventHelper
instanceKlass sun/security/util/MemoryCache$CacheEntry
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a251a0400
instanceKlass sun/security/x509/OIDMap$OIDInfo
instanceKlass sun/security/x509/PKIXExtensions
instanceKlass sun/security/x509/OIDMap
instanceKlass sun/security/x509/Extension
instanceKlass java/security/cert/Extension
instanceKlass sun/security/x509/CertificateExtensions
instanceKlass sun/security/rsa/RSAUtil
instanceKlass java/security/interfaces/RSAPublicKey
instanceKlass java/security/spec/RSAPrivateKeySpec
instanceKlass java/security/spec/RSAPublicKeySpec
instanceKlass  @bci java/security/spec/EncodedKeySpec <clinit> ()V 0 <appendix> argL0 ; # java/security/spec/EncodedKeySpec$$Lambda+0x0000019a251fde20
instanceKlass jdk/internal/access/JavaSecuritySpecAccess
instanceKlass sun/security/x509/CertificateX509Key
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/calendar/CalendarSystem$GregorianHolder
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass sun/security/x509/CertificateValidity
instanceKlass sun/security/x509/AVA
instanceKlass sun/security/x509/RDN
instanceKlass javax/security/auth/x500/X500Principal
instanceKlass  @bci sun/security/x509/X500Name <clinit> ()V 153 <appendix> argL0 ; # sun/security/x509/X500Name$$Lambda+0x0000019a251fc7d0
instanceKlass sun/security/x509/X500Name
instanceKlass sun/security/x509/GeneralNameInterface
instanceKlass sun/security/x509/CertificateAlgorithmId
instanceKlass sun/security/x509/SerialNumber
instanceKlass sun/security/x509/CertificateSerialNumber
instanceKlass sun/security/x509/CertificateVersion
instanceKlass sun/security/x509/X509CertInfo
instanceKlass sun/security/util/Cache$EqualByteArray
instanceKlass java/security/cert/CertificateFactorySpi
instanceKlass java/security/cert/CertificateFactory
instanceKlass java/security/cert/X509Extension
instanceKlass sun/security/ssl/CertificateMessage$CertificateEntry
instanceKlass com/sun/crypto/provider/GaloisCounterMode$DecryptOp
instanceKlass com/sun/crypto/provider/GaloisCounterMode$GCMOperation
instanceKlass com/sun/crypto/provider/GHASH
instanceKlass com/sun/crypto/provider/GCM
instanceKlass com/sun/crypto/provider/GaloisCounterMode$GCMEngine
instanceKlass javax/crypto/spec/GCMParameterSpec
instanceKlass sun/security/ssl/ChangeCipherSpec$T13ChangeCipherSpecConsumer
instanceKlass sun/security/ssl/ChangeCipherSpec$T10ChangeCipherSpecProducer
instanceKlass sun/security/ssl/ChangeCipherSpec$T10ChangeCipherSpecConsumer
instanceKlass sun/security/ssl/ChangeCipherSpec
instanceKlass javax/crypto/spec/IvParameterSpec
instanceKlass sun/security/ssl/SSLTrafficKeyDerivation$T13TrafficKeyDerivation
instanceKlass sun/security/ssl/SSLTrafficKeyDerivation$1
instanceKlass sun/security/ssl/SSLTrafficKeyDerivation$T13TrafficKeyDerivationGenerator
instanceKlass sun/security/ssl/SSLTrafficKeyDerivation$T12TrafficKeyDerivationGenerator
instanceKlass sun/security/ssl/SSLTrafficKeyDerivation$T10TrafficKeyDerivationGenerator
instanceKlass sun/security/ssl/SSLTrafficKeyDerivation$S30TrafficKeyDerivationGenerator
instanceKlass sun/security/ssl/SSLKeyDerivationGenerator
instanceKlass sun/security/ssl/SSLSecretDerivation
instanceKlass javax/crypto/MacSpi
instanceKlass javax/crypto/Mac
instanceKlass sun/security/ssl/HKDF
instanceKlass  @bci javax/crypto/spec/SecretKeySpec <clinit> ()V 0 <appendix> argL0 ; # javax/crypto/spec/SecretKeySpec$$Lambda+0x0000019a251f3ca8
instanceKlass  @cpi javax/crypto/spec/SecretKeySpec 165 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a251a0000
instanceKlass jdk/internal/access/JavaxCryptoSpecAccess
instanceKlass javax/crypto/spec/SecretKeySpec
instanceKlass javax/crypto/KeyAgreementSpi
instanceKlass sun/security/ssl/KAKeyDerivation
instanceKlass java/security/interfaces/RSAKey
instanceKlass javax/crypto/SecretKey
instanceKlass sun/security/util/Length
instanceKlass java/security/spec/EncodedKeySpec
instanceKlass java/security/spec/ECPublicKeySpec
instanceKlass sun/security/ssl/ECDHKeyExchange$ECDHECredentials
instanceKlass sun/security/ssl/NamedGroupCredentials
instanceKlass sun/security/ssl/KeyShareExtension$SHKeyShareSpec
instanceKlass sun/security/ssl/HandshakeHash$CloneableHash
instanceKlass sun/security/ssl/HandshakeHash$T13HandshakeHash
instanceKlass sun/security/ssl/SupportedVersionsExtension$SHSupportedVersionsSpec
instanceKlass sun/security/ssl/TransportContext$1
instanceKlass sun/security/ssl/Plaintext
instanceKlass sun/security/ssl/OutputRecord$T13PaddingHolder
instanceKlass sun/security/ssl/RenegoInfoExtension$RenegotiationInfoSpec
instanceKlass sun/security/ssl/KeyShareExtension$CHKeyShareSpec
instanceKlass java/security/interfaces/ECPublicKey
instanceKlass sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1
instanceKlass sun/security/ec/ECOperations$PointMultiplier$Default
instanceKlass sun/security/ec/ECOperations$PointMultiplier$Secp256R1GeneratorMultiplier$P256
instanceKlass sun/security/ec/ECOperations$PointMultiplier$Secp256R1GeneratorMultiplier
instanceKlass sun/security/ec/ECOperations$PointMultiplier
instanceKlass  @bci sun/security/ec/ECPrivateKeyImpl calculatePublicKey ()Ljava/security/PublicKey; 9 <appendix> argL0 ; # sun/security/ec/ECPrivateKeyImpl$$Lambda+0x0000019a2519ab18
instanceKlass sun/security/util/ArrayUtil
instanceKlass java/security/interfaces/ECPrivateKey
instanceKlass sun/security/ec/ECOperations
instanceKlass sun/security/ssl/ECDHKeyExchange$ECDHEPossession
instanceKlass sun/security/ssl/KeyShareExtension$KeyShareEntry
instanceKlass sun/security/ssl/XDHKeyExchange$1
instanceKlass sun/security/util/DerInputStream
instanceKlass sun/security/util/DerValue
instanceKlass sun/security/pkcs/PKCS8Key
instanceKlass sun/security/util/InternalPrivateKey
instanceKlass java/security/interfaces/XECPrivateKey
instanceKlass sun/security/util/BitArray
instanceKlass sun/security/x509/X509Key
instanceKlass java/security/interfaces/XECPublicKey
instanceKlass java/security/interfaces/XECKey
instanceKlass java/security/KeyPair
instanceKlass java/math/MutableBigInteger
instanceKlass sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Default
instanceKlass sun/security/util/math/IntegerModuloP$MultiplicativeInverser
instanceKlass sun/security/util/math/MutableIntegerModuloP
instanceKlass sun/security/jca/JCAUtil$CachedSecureRandomHolder
instanceKlass sun/security/jca/JCAUtil
instanceKlass sun/security/ec/XECOperations
instanceKlass sun/security/ec/XECParameters
instanceKlass  @bci sun/security/ec/XDHKeyPairGenerator initialize (Ljava/security/spec/AlgorithmParameterSpec;Ljava/security/SecureRandom;)V 0 <appendix> argL0 ; # sun/security/ec/XDHKeyPairGenerator$$Lambda+0x0000019a25199838
instanceKlass sun/security/ssl/XDHKeyExchange$XDHEPossession
instanceKlass sun/security/ssl/ECDHKeyExchange$ECDHEXDHKAGenerator
instanceKlass sun/security/ssl/ECDHKeyExchange$ECDHEKAGenerator
instanceKlass sun/security/ssl/ECDHKeyExchange$ECDHKAGenerator
instanceKlass sun/security/ssl/ECDHKeyExchange$ECDHEPossessionGenerator
instanceKlass sun/security/ssl/ECDHKeyExchange
instanceKlass sun/security/ssl/DHKeyExchange$DHEKAGenerator
instanceKlass sun/security/ssl/DHKeyExchange$DHEPossessionGenerator
instanceKlass sun/security/ssl/DHKeyExchange
instanceKlass sun/security/ssl/RSAKeyExchange$RSAKAGenerator
instanceKlass sun/security/ssl/RSAKeyExchange$EphemeralRSAPossessionGenerator
instanceKlass sun/security/ssl/RSAKeyExchange
instanceKlass sun/security/ssl/SSLKeyExchange$T13KeyAgreement
instanceKlass sun/security/ssl/SSLKeyAgreement
instanceKlass sun/security/ssl/SSLPossessionGenerator
instanceKlass sun/security/ssl/SSLKeyExchange
instanceKlass sun/security/ssl/SSLHandshakeBinding
instanceKlass sun/security/ssl/SSLKeyAgreementGenerator
instanceKlass sun/security/ssl/PskKeyExchangeModesExtension$PskKeyExchangeModesSpec
instanceKlass sun/security/ssl/SupportedVersionsExtension$CHSupportedVersionsSpec
instanceKlass sun/security/ssl/SignatureAlgorithmsExtension$SignatureSchemesSpec
instanceKlass sun/security/ssl/ExtendedMasterSecretExtension$ExtendedMasterSecretSpec
instanceKlass sun/security/ssl/CertStatusExtension$CertStatusRequestV2Spec
instanceKlass sun/security/ssl/ECPointFormatsExtension$ECPointFormatsSpec
instanceKlass sun/security/ssl/SupportedGroupsExtension$SupportedGroupsSpec
instanceKlass sun/security/ssl/CertStatusExtension$CertStatusRequest
instanceKlass sun/security/ssl/CertStatusExtension$CertStatusRequestSpec
instanceKlass sun/security/ssl/ServerNameExtension$CHServerNamesSpec
instanceKlass sun/security/ssl/SSLExtension$SSLExtensionSpec
instanceKlass sun/security/ssl/SSLExtension$ClientExtensions
instanceKlass sun/security/ssl/PreSharedKeyExtension$SHPreSharedKeyStringizer
instanceKlass sun/security/ssl/PreSharedKeyExtension$SHPreSharedKeyAbsence
instanceKlass sun/security/ssl/PreSharedKeyExtension$SHPreSharedKeyConsumer
instanceKlass sun/security/ssl/PreSharedKeyExtension$SHPreSharedKeyProducer
instanceKlass sun/security/ssl/PreSharedKeyExtension$CHPreSharedKeyStringizer
instanceKlass sun/security/ssl/PreSharedKeyExtension$CHPreSharedKeyOnTradeAbsence
instanceKlass sun/security/ssl/PreSharedKeyExtension$CHPreSharedKeyUpdate
instanceKlass sun/security/ssl/PreSharedKeyExtension$CHPreSharedKeyOnLoadAbsence
instanceKlass sun/security/ssl/PreSharedKeyExtension$CHPreSharedKeyConsumer
instanceKlass sun/security/ssl/PreSharedKeyExtension$CHPreSharedKeyProducer
instanceKlass sun/security/ssl/PreSharedKeyExtension
instanceKlass sun/security/ssl/RenegoInfoExtension$RenegotiationInfoStringizer
instanceKlass sun/security/ssl/RenegoInfoExtension$SHRenegotiationInfoAbsence
instanceKlass sun/security/ssl/RenegoInfoExtension$SHRenegotiationInfoConsumer
instanceKlass sun/security/ssl/RenegoInfoExtension$SHRenegotiationInfoProducer
instanceKlass sun/security/ssl/RenegoInfoExtension$CHRenegotiationInfoAbsence
instanceKlass sun/security/ssl/RenegoInfoExtension$CHRenegotiationInfoConsumer
instanceKlass sun/security/ssl/RenegoInfoExtension$CHRenegotiationInfoProducer
instanceKlass sun/security/ssl/RenegoInfoExtension
instanceKlass sun/security/ssl/KeyShareExtension$HRRKeyShareStringizer
instanceKlass sun/security/ssl/KeyShareExtension$HRRKeyShareReproducer
instanceKlass sun/security/ssl/KeyShareExtension$HRRKeyShareConsumer
instanceKlass sun/security/ssl/KeyShareExtension$HRRKeyShareProducer
instanceKlass sun/security/ssl/KeyShareExtension$SHKeyShareStringizer
instanceKlass sun/security/ssl/KeyShareExtension$SHKeyShareAbsence
instanceKlass sun/security/ssl/KeyShareExtension$SHKeyShareConsumer
instanceKlass sun/security/ssl/KeyShareExtension$SHKeyShareProducer
instanceKlass sun/security/ssl/KeyShareExtension$CHKeyShareStringizer
instanceKlass sun/security/ssl/KeyShareExtension$CHKeyShareOnTradeAbsence
instanceKlass sun/security/ssl/KeyShareExtension$CHKeyShareConsumer
instanceKlass sun/security/ssl/KeyShareExtension$CHKeyShareProducer
instanceKlass sun/security/ssl/KeyShareExtension
instanceKlass sun/security/ssl/CertSignAlgsExtension$CertSignatureSchemesStringizer
instanceKlass sun/security/ssl/CertSignAlgsExtension$CRCertSignatureSchemesUpdate
instanceKlass sun/security/ssl/CertSignAlgsExtension$CRCertSignatureSchemesConsumer
instanceKlass sun/security/ssl/CertSignAlgsExtension$CRCertSignatureSchemesProducer
instanceKlass sun/security/ssl/CertSignAlgsExtension$CHCertSignatureSchemesUpdate
instanceKlass sun/security/ssl/CertSignAlgsExtension$CHCertSignatureSchemesConsumer
instanceKlass sun/security/ssl/CertSignAlgsExtension$CHCertSignatureSchemesProducer
instanceKlass sun/security/ssl/CertSignAlgsExtension
instanceKlass sun/security/ssl/CertificateAuthoritiesExtension$CertificateAuthoritiesStringizer
instanceKlass sun/security/ssl/CertificateAuthoritiesExtension$CRCertificateAuthoritiesConsumer
instanceKlass sun/security/ssl/CertificateAuthoritiesExtension$CRCertificateAuthoritiesProducer
instanceKlass sun/security/ssl/CertificateAuthoritiesExtension$CHCertificateAuthoritiesConsumer
instanceKlass sun/security/ssl/CertificateAuthoritiesExtension$CHCertificateAuthoritiesProducer
instanceKlass sun/security/ssl/CertificateAuthoritiesExtension
instanceKlass sun/security/ssl/PskKeyExchangeModesExtension$PskKeyExchangeModesStringizer
instanceKlass sun/security/ssl/PskKeyExchangeModesExtension$PskKeyExchangeModesOnTradeAbsence
instanceKlass sun/security/ssl/PskKeyExchangeModesExtension$PskKeyExchangeModesOnLoadAbsence
instanceKlass sun/security/ssl/PskKeyExchangeModesExtension$PskKeyExchangeModesConsumer
instanceKlass sun/security/ssl/PskKeyExchangeModesExtension$PskKeyExchangeModesProducer
instanceKlass sun/security/ssl/PskKeyExchangeModesExtension
instanceKlass sun/security/ssl/CookieExtension$CookieStringizer
instanceKlass sun/security/ssl/CookieExtension$HRRCookieReproducer
instanceKlass sun/security/ssl/CookieExtension$HRRCookieConsumer
instanceKlass sun/security/ssl/CookieExtension$HRRCookieProducer
instanceKlass sun/security/ssl/CookieExtension$CHCookieUpdate
instanceKlass sun/security/ssl/CookieExtension$CHCookieConsumer
instanceKlass sun/security/ssl/CookieExtension$CHCookieProducer
instanceKlass sun/security/ssl/CookieExtension
instanceKlass sun/security/ssl/SupportedVersionsExtension$HRRSupportedVersionsReproducer
instanceKlass sun/security/ssl/SupportedVersionsExtension$HRRSupportedVersionsConsumer
instanceKlass sun/security/ssl/SupportedVersionsExtension$HRRSupportedVersionsProducer
instanceKlass sun/security/ssl/SupportedVersionsExtension$SHSupportedVersionsStringizer
instanceKlass sun/security/ssl/SupportedVersionsExtension$SHSupportedVersionsConsumer
instanceKlass sun/security/ssl/SupportedVersionsExtension$SHSupportedVersionsProducer
instanceKlass sun/security/ssl/SupportedVersionsExtension$CHSupportedVersionsStringizer
instanceKlass sun/security/ssl/SupportedVersionsExtension$CHSupportedVersionsConsumer
instanceKlass sun/security/ssl/SupportedVersionsExtension$CHSupportedVersionsProducer
instanceKlass sun/security/ssl/SupportedVersionsExtension
instanceKlass sun/security/ssl/SignatureAlgorithmsExtension$SignatureSchemesStringizer
instanceKlass sun/security/ssl/SignatureAlgorithmsExtension$CRSignatureSchemesUpdate
instanceKlass sun/security/ssl/SignatureAlgorithmsExtension$CRSignatureSchemesAbsence
instanceKlass sun/security/ssl/SignatureAlgorithmsExtension$CRSignatureSchemesConsumer
instanceKlass sun/security/ssl/SignatureAlgorithmsExtension$CRSignatureSchemesProducer
instanceKlass sun/security/ssl/SignatureAlgorithmsExtension$CHSignatureSchemesOnTradeAbsence
instanceKlass sun/security/ssl/SignatureAlgorithmsExtension$CHSignatureSchemesUpdate
instanceKlass sun/security/ssl/SignatureAlgorithmsExtension$CHSignatureSchemesOnLoadAbsence
instanceKlass sun/security/ssl/SignatureAlgorithmsExtension$CHSignatureSchemesConsumer
instanceKlass sun/security/ssl/SignatureAlgorithmsExtension$CHSignatureSchemesProducer
instanceKlass sun/security/ssl/SignatureAlgorithmsExtension
instanceKlass sun/security/ssl/SessionTicketExtension$SessionTicketStringizer
instanceKlass sun/security/ssl/SessionTicketExtension$T12SHSessionTicketConsumer
instanceKlass sun/security/ssl/SessionTicketExtension$T12SHSessionTicketProducer
instanceKlass sun/security/ssl/SessionTicketExtension$T12CHSessionTicketConsumer
instanceKlass sun/security/ssl/SessionTicketExtension$T12CHSessionTicketProducer
instanceKlass sun/security/ssl/SessionTicketExtension
instanceKlass sun/security/ssl/ExtendedMasterSecretExtension$ExtendedMasterSecretStringizer
instanceKlass sun/security/ssl/ExtendedMasterSecretExtension$SHExtendedMasterSecretAbsence
instanceKlass sun/security/ssl/ExtendedMasterSecretExtension$SHExtendedMasterSecretConsumer
instanceKlass sun/security/ssl/ExtendedMasterSecretExtension$SHExtendedMasterSecretProducer
instanceKlass sun/security/ssl/ExtendedMasterSecretExtension$CHExtendedMasterSecretAbsence
instanceKlass sun/security/ssl/ExtendedMasterSecretExtension$CHExtendedMasterSecretConsumer
instanceKlass sun/security/ssl/ExtendedMasterSecretExtension$CHExtendedMasterSecretProducer
instanceKlass sun/security/ssl/ExtendedMasterSecretExtension
instanceKlass  @bci sun/security/ssl/AlpnExtension <clinit> ()V 100 <appendix> argL0 ; # sun/security/ssl/AlpnExtension$$Lambda+0x0000019a251d7e10
instanceKlass sun/security/ssl/AlpnExtension$AlpnStringizer
instanceKlass sun/security/ssl/AlpnExtension$SHAlpnAbsence
instanceKlass sun/security/ssl/AlpnExtension$SHAlpnConsumer
instanceKlass sun/security/ssl/AlpnExtension$SHAlpnProducer
instanceKlass sun/security/ssl/AlpnExtension$CHAlpnAbsence
instanceKlass sun/security/ssl/AlpnExtension$CHAlpnConsumer
instanceKlass sun/security/ssl/AlpnExtension$CHAlpnProducer
instanceKlass sun/security/ssl/AlpnExtension
instanceKlass sun/security/ssl/ECPointFormatsExtension$ECPointFormatsStringizer
instanceKlass sun/security/ssl/ECPointFormatsExtension$SHECPointFormatsConsumer
instanceKlass sun/security/ssl/ECPointFormatsExtension$CHECPointFormatsConsumer
instanceKlass sun/security/ssl/ECPointFormatsExtension$CHECPointFormatsProducer
instanceKlass sun/security/ssl/ECPointFormatsExtension
instanceKlass sun/security/ssl/SupportedGroupsExtension$EESupportedGroupsConsumer
instanceKlass sun/security/ssl/SupportedGroupsExtension$EESupportedGroupsProducer
instanceKlass sun/security/ssl/SupportedGroupsExtension$SupportedGroupsStringizer
instanceKlass sun/security/ssl/SupportedGroupsExtension$CHSupportedGroupsOnTradeAbsence
instanceKlass sun/security/ssl/SupportedGroupsExtension$CHSupportedGroupsConsumer
instanceKlass sun/security/ssl/SupportedGroupsExtension$CHSupportedGroupsProducer
instanceKlass sun/security/ssl/SupportedGroupsExtension
instanceKlass sun/security/ssl/CertStatusExtension$CertStatusRespStringizer
instanceKlass sun/security/ssl/CertStatusExtension$CertStatusRequestsStringizer
instanceKlass sun/security/ssl/CertStatusExtension$SHCertStatusReqV2Consumer
instanceKlass sun/security/ssl/CertStatusExtension$SHCertStatusReqV2Producer
instanceKlass sun/security/ssl/CertStatusExtension$CHCertStatusReqV2Consumer
instanceKlass sun/security/ssl/CertStatusExtension$CHCertStatusReqV2Producer
instanceKlass sun/security/ssl/CertStatusExtension$CertStatusRequestStringizer
instanceKlass sun/security/ssl/CertStatusExtension$CTCertStatusResponseConsumer
instanceKlass sun/security/ssl/CertStatusExtension$CTCertStatusResponseProducer
instanceKlass sun/security/ssl/CertStatusExtension$SHCertStatusReqConsumer
instanceKlass sun/security/ssl/CertStatusExtension$SHCertStatusReqProducer
instanceKlass sun/security/ssl/CertStatusExtension$CHCertStatusReqConsumer
instanceKlass sun/security/ssl/CertStatusExtension$CHCertStatusReqProducer
instanceKlass sun/security/ssl/CertStatusExtension
instanceKlass sun/security/ssl/MaxFragExtension$MaxFragLenStringizer
instanceKlass sun/security/ssl/MaxFragExtension$EEMaxFragmentLengthUpdate
instanceKlass sun/security/ssl/MaxFragExtension$EEMaxFragmentLengthConsumer
instanceKlass sun/security/ssl/MaxFragExtension$EEMaxFragmentLengthProducer
instanceKlass sun/security/ssl/MaxFragExtension$SHMaxFragmentLengthUpdate
instanceKlass sun/security/ssl/MaxFragExtension$SHMaxFragmentLengthConsumer
instanceKlass sun/security/ssl/MaxFragExtension$SHMaxFragmentLengthProducer
instanceKlass sun/security/ssl/MaxFragExtension$CHMaxFragmentLengthConsumer
instanceKlass sun/security/ssl/MaxFragExtension$CHMaxFragmentLengthProducer
instanceKlass sun/security/ssl/MaxFragExtension
instanceKlass sun/security/ssl/ServerNameExtension$EEServerNameConsumer
instanceKlass sun/security/ssl/ServerNameExtension$EEServerNameProducer
instanceKlass sun/security/ssl/ServerNameExtension$SHServerNamesStringizer
instanceKlass sun/security/ssl/ServerNameExtension$SHServerNameConsumer
instanceKlass sun/security/ssl/ServerNameExtension$SHServerNameProducer
instanceKlass sun/security/ssl/ServerNameExtension$CHServerNamesStringizer
instanceKlass sun/security/ssl/ServerNameExtension$CHServerNameConsumer
instanceKlass sun/security/ssl/SSLExtension$ExtensionConsumer
instanceKlass sun/security/ssl/ServerNameExtension$CHServerNameProducer
instanceKlass sun/security/ssl/ServerNameExtension
instanceKlass sun/security/ssl/SSLStringizer
instanceKlass sun/security/ssl/SSLExtensions
instanceKlass sun/security/ssl/SSLHandshake$HandshakeMessage
instanceKlass sun/security/ssl/RandomCookie
instanceKlass java/lang/Byte$ByteCache
instanceKlass sun/security/util/KeyUtil
instanceKlass sun/security/ssl/SSLKeyDerivation
instanceKlass sun/security/ssl/SSLCredentials
instanceKlass sun/security/ssl/NamedGroupPossession
instanceKlass sun/security/ssl/SSLPossession
instanceKlass sun/security/ssl/HandshakeContext
instanceKlass  @bci com/mysql/cj/protocol/ExportControlled lambda$getAllowedCiphers$5 (Ljava/lang/String;)Z 14 <appendix> member <vmtarget> ; # com/mysql/cj/protocol/ExportControlled$$Lambda+0x0000019a2518f210
instanceKlass  @bci com/mysql/cj/protocol/ExportControlled getAllowedCiphers (Lcom/mysql/cj/conf/PropertySet;Ljava/util/List;)[Ljava/lang/String; 79 <appendix> argL0 ; # com/mysql/cj/protocol/ExportControlled$$Lambda+0x0000019a2518efb0
instanceKlass  @bci com/mysql/cj/protocol/ExportControlled getAllowedCiphers (Lcom/mysql/cj/conf/PropertySet;Ljava/util/List;)[Ljava/lang/String; 69 <appendix> member <vmtarget> ; # com/mysql/cj/protocol/ExportControlled$$Lambda+0x0000019a2518ed48
instanceKlass jdk/internal/icu/impl/Trie2$UTrie2Header
instanceKlass jdk/internal/icu/impl/Trie2$1
instanceKlass jdk/internal/icu/impl/Trie2$ValueMapper
instanceKlass jdk/internal/icu/impl/Trie2
instanceKlass jdk/internal/icu/impl/UCharacterProperty$IsAcceptable
instanceKlass jdk/internal/icu/impl/ICUBinary$1
instanceKlass jdk/internal/icu/impl/UCharacterProperty$IntProperty
instanceKlass jdk/internal/icu/impl/UCharacterProperty
instanceKlass jdk/internal/icu/lang/UCharacter
instanceKlass jdk/internal/icu/util/VersionInfo
instanceKlass jdk/internal/icu/impl/Trie
instanceKlass jdk/internal/icu/text/StringPrep$StringPrepTrieImpl
instanceKlass jdk/internal/icu/impl/Trie$DataManipulate
instanceKlass jdk/internal/icu/impl/ICUBinary
instanceKlass jdk/internal/icu/impl/StringPrepDataReader
instanceKlass jdk/internal/icu/impl/ICUBinary$Authenticate
instanceKlass jdk/internal/icu/text/StringPrep
instanceKlass java/net/IDN
instanceKlass javax/net/ssl/SNIServerName
instanceKlass sun/security/ssl/DTLSRecord
instanceKlass sun/security/ssl/SessionId
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass sun/security/x509/AlgorithmId
instanceKlass java/security/spec/MGF1ParameterSpec
instanceKlass java/security/spec/PSSParameterSpec
instanceKlass sun/security/ec/ParametersMap$1
instanceKlass  @bci sun/security/ec/ed/EdDSAParameters <clinit> ()V 268 <appendix> argL0 ; # sun/security/ec/ed/EdDSAParameters$$Lambda+0x0000019a25198c18
instanceKlass sun/security/ec/ed/EdDSAParameters$SHAKE256DigesterFactory
instanceKlass sun/security/ec/point/ProjectivePoint
instanceKlass  @bci sun/security/ec/ed/EdDSAParameters <clinit> ()V 117 <appendix> argL0 ; # sun/security/ec/ed/EdDSAParameters$$Lambda+0x0000019a25198210
instanceKlass sun/security/ec/ed/EdDSAParameters$Digester
instanceKlass sun/security/ec/ed/EdDSAParameters$SHA512DigesterFactory
instanceKlass sun/security/ec/point/ExtendedHomogeneousPoint
instanceKlass sun/security/ec/point/AffinePoint
instanceKlass sun/security/util/math/intpoly/IntegerPolynomial$Limb
instanceKlass sun/security/util/math/SmallValue
instanceKlass sun/security/ec/point/MutablePoint
instanceKlass sun/security/ec/point/ImmutablePoint
instanceKlass sun/security/ec/point/Point
instanceKlass sun/security/util/math/intpoly/IntegerPolynomial$Element
instanceKlass sun/security/util/math/ImmutableIntegerModuloP
instanceKlass sun/security/util/math/IntegerModuloP
instanceKlass sun/security/util/math/intpoly/IntegerPolynomial
instanceKlass sun/security/ec/ParametersMap
instanceKlass sun/security/ec/ed/EdECOperations
instanceKlass sun/security/ec/ed/EdDSAParameters$DigesterFactory
instanceKlass sun/security/util/math/IntegerFieldModuloP
instanceKlass sun/security/ec/ed/EdDSAParameters
instanceKlass  @bci sun/security/ec/ed/EdDSASignature <init> (Ljava/security/spec/NamedParameterSpec;)V 27 <appendix> argL0 ; # sun/security/ec/ed/EdDSASignature$$Lambda+0x0000019a25195dd0
instanceKlass java/security/spec/EdDSAParameterSpec
instanceKlass sun/security/ec/ed/EdDSASignature$MessageAccumulator
instanceKlass javax/net/ssl/ExtendedSSLSession
instanceKlass javax/net/ssl/SSLSession
instanceKlass sun/security/ssl/PredefinedDHParameterSpecs$1
instanceKlass  @bci java/util/regex/Pattern Range (II)Ljava/util/regex/Pattern$CharPredicate; 23 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x80000002a
instanceKlass sun/security/ssl/PredefinedDHParameterSpecs
instanceKlass  @bci sun/security/provider/certpath/ldap/JdkLDAP <init> ()V 15 <appendix> member <vmtarget> ; # sun/security/provider/certpath/ldap/JdkLDAP$$Lambda+0x0000019a251c3b70
instanceKlass com/sun/security/sasl/Provider$1
instanceKlass com/sun/security/sasl/gsskerb/JdkSASL$1
instanceKlass sun/security/smartcardio/SunPCSC$1
instanceKlass sun/security/ssl/NamedGroup$SupportedGroups
instanceKlass sun/security/ssl/SSLConfiguration$CustomizedClientSignatureSchemes
instanceKlass javax/crypto/KeyGeneratorSpi
instanceKlass javax/crypto/KeyGenerator
instanceKlass sun/security/ssl/SSLConfiguration
instanceKlass sun/security/ssl/SSLCipher$SSLWriteCipher
instanceKlass sun/security/ssl/KeyUpdate$KeyUpdateProducer
instanceKlass sun/security/ssl/KeyUpdate$KeyUpdateConsumer
instanceKlass sun/security/ssl/KeyUpdate$KeyUpdateKickstartProducer
instanceKlass sun/security/ssl/KeyUpdate
instanceKlass sun/security/ssl/CertificateStatus$CertificateStatusAbsence
instanceKlass sun/security/ssl/HandshakeAbsence
instanceKlass sun/security/ssl/CertificateStatus$CertificateStatusProducer
instanceKlass sun/security/ssl/CertificateStatus$CertificateStatusConsumer
instanceKlass sun/security/ssl/CertificateStatus
instanceKlass sun/security/ssl/Finished$T13FinishedProducer
instanceKlass sun/security/ssl/Finished$T13FinishedConsumer
instanceKlass sun/security/ssl/Finished$T12FinishedProducer
instanceKlass sun/security/ssl/Finished$T12FinishedConsumer
instanceKlass sun/security/ssl/Finished
instanceKlass sun/security/ssl/ClientKeyExchange$ClientKeyExchangeProducer
instanceKlass sun/security/ssl/ClientKeyExchange$ClientKeyExchangeConsumer
instanceKlass sun/security/ssl/ClientKeyExchange
instanceKlass sun/security/ssl/CertificateVerify$T13CertificateVerifyProducer
instanceKlass sun/security/ssl/CertificateVerify$T13CertificateVerifyConsumer
instanceKlass sun/security/ssl/CertificateVerify$T12CertificateVerifyProducer
instanceKlass sun/security/ssl/CertificateVerify$T12CertificateVerifyConsumer
instanceKlass sun/security/ssl/CertificateVerify$T10CertificateVerifyProducer
instanceKlass sun/security/ssl/CertificateVerify$T10CertificateVerifyConsumer
instanceKlass sun/security/ssl/CertificateVerify$S30CertificateVerifyProducer
instanceKlass sun/security/ssl/CertificateVerify$S30CertificateVerifyConsumer
instanceKlass sun/security/ssl/CertificateVerify
instanceKlass sun/security/ssl/ServerHelloDone$ServerHelloDoneProducer
instanceKlass sun/security/ssl/ServerHelloDone$ServerHelloDoneConsumer
instanceKlass sun/security/ssl/ServerHelloDone
instanceKlass sun/security/ssl/CertificateRequest$T13CertificateRequestProducer
instanceKlass sun/security/ssl/CertificateRequest$T13CertificateRequestConsumer
instanceKlass sun/security/ssl/CertificateRequest$T12CertificateRequestProducer
instanceKlass sun/security/ssl/CertificateRequest$T12CertificateRequestConsumer
instanceKlass sun/security/ssl/CertificateRequest$T10CertificateRequestProducer
instanceKlass sun/security/ssl/CertificateRequest$T10CertificateRequestConsumer
instanceKlass sun/security/ssl/CertificateRequest
instanceKlass sun/security/ssl/ServerKeyExchange$ServerKeyExchangeProducer
instanceKlass sun/security/ssl/ServerKeyExchange$ServerKeyExchangeConsumer
instanceKlass sun/security/ssl/ServerKeyExchange
instanceKlass sun/security/ssl/CertificateMessage$T13CertificateProducer
instanceKlass sun/security/ssl/CertificateMessage$T13CertificateConsumer
instanceKlass sun/security/ssl/CertificateMessage$T12CertificateProducer
instanceKlass sun/security/ssl/CertificateMessage$T12CertificateConsumer
instanceKlass sun/security/ssl/CertificateMessage
instanceKlass sun/security/ssl/EncryptedExtensions$EncryptedExtensionsConsumer
instanceKlass sun/security/ssl/EncryptedExtensions$EncryptedExtensionsProducer
instanceKlass sun/security/ssl/EncryptedExtensions
instanceKlass sun/security/ssl/NewSessionTicket$T12NewSessionTicketProducer
instanceKlass sun/security/ssl/NewSessionTicket$T13NewSessionTicketProducer
instanceKlass sun/security/ssl/NewSessionTicket$T12NewSessionTicketConsumer
instanceKlass sun/security/ssl/NewSessionTicket$T13NewSessionTicketConsumer
instanceKlass sun/security/ssl/NewSessionTicket
instanceKlass sun/security/ssl/HelloVerifyRequest$HelloVerifyRequestProducer
instanceKlass sun/security/ssl/HelloVerifyRequest$HelloVerifyRequestConsumer
instanceKlass sun/security/ssl/HelloVerifyRequest
instanceKlass sun/security/ssl/ServerHello$T13HelloRetryRequestConsumer
instanceKlass sun/security/ssl/ServerHello$T13ServerHelloConsumer
instanceKlass sun/security/ssl/ServerHello$T12ServerHelloConsumer
instanceKlass sun/security/ssl/ServerHello$T13HelloRetryRequestReproducer
instanceKlass sun/security/ssl/ServerHello$T13HelloRetryRequestProducer
instanceKlass sun/security/ssl/ServerHello$T13ServerHelloProducer
instanceKlass sun/security/ssl/ServerHello$T12ServerHelloProducer
instanceKlass sun/security/ssl/ServerHello$ServerHelloConsumer
instanceKlass sun/security/ssl/ServerHello
instanceKlass sun/security/ssl/ClientHello$D13ClientHelloConsumer
instanceKlass sun/security/ssl/ClientHello$D12ClientHelloConsumer
instanceKlass sun/security/ssl/ClientHello$T13ClientHelloConsumer
instanceKlass sun/security/ssl/ClientHello$T12ClientHelloConsumer
instanceKlass sun/security/ssl/HandshakeConsumer
instanceKlass sun/security/ssl/ClientHello$ClientHelloProducer
instanceKlass sun/security/ssl/ClientHello$ClientHelloConsumer
instanceKlass sun/security/ssl/ClientHello$ClientHelloKickstartProducer
instanceKlass sun/security/ssl/ClientHello
instanceKlass sun/security/ssl/HelloRequest$HelloRequestProducer
instanceKlass sun/security/ssl/HelloRequest$HelloRequestConsumer
instanceKlass sun/security/ssl/HelloRequest$HelloRequestKickstartProducer
instanceKlass sun/security/ssl/SSLProducer
instanceKlass sun/security/ssl/HelloRequest
instanceKlass sun/security/ssl/HandshakeProducer
instanceKlass sun/security/ssl/SSLConsumer
instanceKlass sun/security/ssl/Authenticator$MacImpl
instanceKlass sun/security/ssl/Authenticator$MAC
instanceKlass sun/security/ssl/Authenticator
instanceKlass sun/security/ssl/SSLCipher$SSLReadCipher
instanceKlass sun/security/ssl/InputRecord
instanceKlass sun/security/ssl/SSLRecord
instanceKlass sun/security/ssl/Record
instanceKlass sun/security/ssl/TransportContext
instanceKlass sun/security/ssl/ConnectionContext
instanceKlass sun/security/ssl/HandshakeHash$CacheOnlyHash
instanceKlass sun/security/ssl/HandshakeHash$TranscriptHash
instanceKlass sun/security/ssl/HandshakeHash
instanceKlass sun/security/ssl/SSLTransport
instanceKlass javax/net/SocketFactory
instanceKlass  @bci sun/security/ssl/SSLContextImpl engineInit ([Ljavax/net/ssl/KeyManager;[Ljavax/net/ssl/TrustManager;Ljava/security/SecureRandom;)V 57 <appendix> argL0 ; # sun/security/ssl/SSLContextImpl$$Lambda+0x0000019a25171700
instanceKlass javax/net/ssl/X509ExtendedTrustManager
instanceKlass javax/net/ssl/X509ExtendedKeyManager
instanceKlass javax/net/ssl/X509KeyManager
instanceKlass javax/net/ssl/KeyManager
instanceKlass sun/security/util/Cache
instanceKlass sun/security/ssl/SSLSessionContextImpl
instanceKlass javax/net/ssl/SSLSessionContext
instanceKlass sun/security/ssl/EphemeralKeyManager$EphemeralKeyPair
instanceKlass sun/security/ssl/EphemeralKeyManager
instanceKlass sun/security/ssl/SSLContextImpl$CustomizedSSLProtocols
instanceKlass java/security/spec/NamedParameterSpec
instanceKlass sun/security/util/ECKeySizeParameterSpec
instanceKlass java/security/AlgorithmParametersSpi
instanceKlass java/security/AlgorithmParameters
instanceKlass sun/security/util/ECUtil
instanceKlass java/security/KeyPairGeneratorSpi
instanceKlass java/security/KeyFactory
instanceKlass javax/crypto/KeyAgreement
instanceKlass java/security/interfaces/ECKey
instanceKlass java/security/Signature$1
instanceKlass jdk/internal/access/JavaSecuritySignatureAccess
instanceKlass java/security/SignatureSpi
instanceKlass sun/security/ssl/JsseJce$EcAvailability
instanceKlass sun/security/ssl/SSLAlgorithmDecomposer$1
instanceKlass sun/security/ssl/Utilities
instanceKlass sun/security/ssl/JsseJce
instanceKlass sun/security/ssl/NamedGroup$XDHScheme
instanceKlass sun/security/ssl/NamedGroup$FFDHEScheme
instanceKlass sun/security/ssl/NamedGroup$ECDHEScheme
instanceKlass sun/security/ssl/NamedGroup$NamedGroupScheme
instanceKlass sun/security/ssl/SSLCipher$1
instanceKlass sun/security/ssl/SSLCipher$T13CC20P1305WriteCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$T12CC20P1305WriteCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$T13CC20P1305ReadCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$T12CC20P1305ReadCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$T13GcmWriteCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$T13GcmReadCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$T12GcmWriteCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$T12GcmReadCipherGenerator
instanceKlass com/sun/crypto/provider/AESConstants
instanceKlass java/util/Vector$1
instanceKlass  @bci javax/crypto/JceSecurityManager <clinit> ()V 67 <appendix> argL0 ; # javax/crypto/JceSecurityManager$$Lambda+0x0000019a25166d30
instanceKlass  @bci javax/crypto/JceSecurityManager <clinit> ()V 49 <appendix> argL0 ; # javax/crypto/JceSecurityManager$$Lambda+0x0000019a25166b00
instanceKlass javax/crypto/JceSecurityManager
instanceKlass sun/security/ssl/SSLCipher$T11BlockWriteCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$T11BlockReadCipherGenerator
instanceKlass com/sun/crypto/provider/PKCS5Padding
instanceKlass com/sun/crypto/provider/Padding
instanceKlass com/sun/crypto/provider/FeedbackCipher
instanceKlass com/sun/crypto/provider/SymmetricCipher
instanceKlass com/sun/crypto/provider/DESConstants
instanceKlass com/sun/crypto/provider/CipherCore
instanceKlass sun/security/ssl/SSLCipher$T10BlockWriteCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$T10BlockReadCipherGenerator
instanceKlass javax/crypto/CipherSpi
instanceKlass javax/crypto/ProviderVerifier
instanceKlass javax/crypto/JceSecurity$3
instanceKlass javax/crypto/JceSecurity$2
instanceKlass java/net/spi/URLStreamHandlerProvider
instanceKlass java/net/URL$1
instanceKlass java/net/URL$2
instanceKlass jdk/internal/misc/ThreadTracker
instanceKlass java/net/URL$ThreadTrackHolder
instanceKlass java/util/Vector$Itr
instanceKlass javax/crypto/CryptoPolicyParser$CryptoPermissionEntry
instanceKlass javax/crypto/CryptoPolicyParser$GrantEntry
instanceKlass java/io/StreamTokenizer
instanceKlass javax/crypto/CryptoPolicyParser
instanceKlass java/nio/channels/NetworkChannel
instanceKlass sun/nio/ch/SelChImpl
instanceKlass sun/nio/ch/Streams
instanceKlass java/nio/channels/Channels
instanceKlass sun/nio/ch/FileChannelImpl$Closer
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass sun/nio/fs/WindowsSecurityDescriptor
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass java/nio/file/Files$1
instanceKlass sun/nio/fs/WindowsFileSystem$2
instanceKlass java/nio/file/PathMatcher
instanceKlass sun/nio/fs/Globs
instanceKlass sun/nio/fs/WindowsChannelFactory$Flags
instanceKlass sun/nio/fs/WindowsChannelFactory$1
instanceKlass sun/nio/fs/WindowsChannelFactory
instanceKlass sun/nio/fs/WindowsFileSystemProvider$1
instanceKlass javax/crypto/JceSecurity$1
instanceKlass javax/crypto/JceSecurity
instanceKlass sun/security/jca/ProviderList$ServiceList$1
instanceKlass sun/security/jca/ServiceId
instanceKlass javax/crypto/Cipher$Transform
instanceKlass javax/crypto/Cipher
instanceKlass sun/security/ssl/SSLCipher$StreamWriteCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$StreamReadCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$NullWriteCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$WriteCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$NullReadCipherGenerator
instanceKlass sun/security/ssl/SSLCipher$ReadCipherGenerator
instanceKlass java/time/zone/ZoneOffsetTransition
instanceKlass java/time/zone/ZoneOffsetTransitionRule
instanceKlass java/time/zone/ZoneRules
instanceKlass  @bci java/time/ZoneOffset ofTotalSeconds (I)Ljava/time/ZoneOffset; 37 <appendix> argL0 ; # java/time/ZoneOffset$$Lambda+0x80000000c
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints$Holder
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraint
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints
instanceKlass sun/security/util/AbstractAlgorithmConstraints$1
instanceKlass sun/security/util/AlgorithmDecomposer
instanceKlass sun/security/util/AbstractAlgorithmConstraints
instanceKlass sun/security/ssl/SSLAlgorithmConstraints
instanceKlass java/security/AlgorithmConstraints
instanceKlass sun/security/ssl/SSLLogger
instanceKlass javax/net/ssl/SSLContextSpi
instanceKlass javax/net/ssl/SSLContext
instanceKlass java/security/cert/CertPathParameters
instanceKlass com/mysql/cj/protocol/ExportControlled$X509TrustManagerWrapper
instanceKlass javax/net/ssl/X509TrustManager
instanceKlass javax/net/ssl/TrustManagerFactorySpi
instanceKlass  @bci javax/net/ssl/TrustManagerFactory getDefaultAlgorithm ()Ljava/lang/String; 0 <appendix> argL0 ; # javax/net/ssl/TrustManagerFactory$$Lambda+0x0000019a25151310
instanceKlass javax/net/ssl/TrustManagerFactory
instanceKlass  @bci sun/security/ssl/SunJSSE registerAlgorithms ()V 1 <appendix> member <vmtarget> ; # sun/security/ssl/SunJSSE$$Lambda+0x0000019a25150a48
instanceKlass java/security/spec/ECFieldF2m
instanceKlass sun/security/util/ObjectIdentifier
instanceKlass sun/security/util/ByteArrayTagOrder
instanceKlass sun/security/util/ByteArrayLexOrder
instanceKlass sun/security/util/DerEncoder
instanceKlass java/security/spec/ECParameterSpec
instanceKlass java/security/spec/ECPoint
instanceKlass java/security/spec/EllipticCurve
instanceKlass java/security/spec/ECFieldFp
instanceKlass java/security/spec/ECField
instanceKlass sun/security/util/CurveDB
instanceKlass sun/security/ec/SunEC$1
instanceKlass  @bci sun/security/pkcs11/SunPKCS11 register (Lsun/security/pkcs11/SunPKCS11$Descriptor;)V 27 <appendix> argL0 ; # sun/security/pkcs11/SunPKCS11$$Lambda+0x0000019a25191bf0
instanceKlass sun/security/pkcs11/SunPKCS11$Descriptor
instanceKlass javax/security/auth/Subject
instanceKlass javax/security/auth/callback/CallbackHandler
instanceKlass org/jcp/xml/dsig/internal/dom/XMLDSigRI$2
instanceKlass org/jcp/xml/dsig/internal/dom/XMLDSigRI$1
instanceKlass sun/security/mscapi/SunMSCAPI$2
instanceKlass sun/security/mscapi/SunMSCAPI$1
instanceKlass  @bci sun/security/jgss/SunProvider <init> ()V 15 <appendix> member <vmtarget> ; # sun/security/jgss/SunProvider$$Lambda+0x0000019a2513f5b8
instanceKlass sun/security/jca/ProviderConfig$ProviderLoader
instanceKlass sun/security/jca/ProviderConfig$3
instanceKlass sun/security/rsa/SunRsaSignEntries
instanceKlass javax/net/ssl/KeyManagerFactorySpi
instanceKlass  @bci javax/net/ssl/KeyManagerFactory getDefaultAlgorithm ()Ljava/lang/String; 0 <appendix> argL0 ; # javax/net/ssl/KeyManagerFactory$$Lambda+0x0000019a2514c240
instanceKlass javax/net/ssl/KeyManagerFactory
instanceKlass javax/net/ssl/TrustManager
instanceKlass com/mysql/cj/protocol/ExportControlled$SslContextBuilder
instanceKlass  @bci java/security/KeyStore getDefaultType ()Ljava/lang/String; 0 <appendix> argL0 ; # java/security/KeyStore$$Lambda+0x0000019a2514ada8
instanceKlass java/security/KeyStore
instanceKlass com/mysql/cj/protocol/ExportControlled$KeyStoreConfigurations
instanceKlass com/mysql/cj/protocol/a/NativeUtils
instanceKlass com/mysql/cj/protocol/a/PacketSplitter
instanceKlass  @bci com/mysql/cj/protocol/ExportControlled <clinit> ()V 169 <appendix> argL0 ; # com/mysql/cj/protocol/ExportControlled$$Lambda+0x0000019a2518dfc8
instanceKlass  @bci com/mysql/cj/protocol/ExportControlled <clinit> ()V 145 <appendix> argL0 ; # com/mysql/cj/protocol/ExportControlled$$Lambda+0x0000019a2518dd88
instanceKlass  @bci com/mysql/cj/protocol/ExportControlled <clinit> ()V 121 <appendix> argL0 ; # com/mysql/cj/protocol/ExportControlled$$Lambda+0x0000019a2518db48
instanceKlass  @bci com/mysql/cj/protocol/ExportControlled <clinit> ()V 97 <appendix> argL0 ; # com/mysql/cj/protocol/ExportControlled$$Lambda+0x0000019a2518d908
instanceKlass com/mysql/cj/protocol/ExportControlled
instanceKlass com/mysql/cj/protocol/a/NativePacketPayload$1
instanceKlass com/mysql/cj/protocol/a/NativeCapabilities
instanceKlass com/mysql/cj/protocol/a/NativePacketHeader
instanceKlass com/mysql/cj/Collation
instanceKlass java/nio/charset/Charset$ExtendedProviderHolder$1
instanceKlass java/nio/charset/Charset$ExtendedProviderHolder
instanceKlass  @bci com/mysql/cj/MysqlCharset <init> (Ljava/lang/String;II[Ljava/lang/String;Lcom/mysql/cj/ServerVersion;)V 81 <appendix> member <vmtarget> ; # com/mysql/cj/MysqlCharset$$Lambda+0x0000019a2518b6c8
instanceKlass com/mysql/cj/ServerVersion
instanceKlass com/mysql/cj/MysqlCharset
instanceKlass com/mysql/cj/CharsetMapping
instanceKlass com/mysql/cj/protocol/a/NativeServerSessionStateController
instanceKlass com/mysql/cj/protocol/ServerSessionStateController
instanceKlass com/mysql/cj/protocol/a/NativeServerSession
instanceKlass com/mysql/cj/protocol/a/BinaryResultsetReader
instanceKlass com/mysql/cj/protocol/a/TextResultsetReader
instanceKlass com/mysql/cj/protocol/a/ResultsetRowReader
instanceKlass com/mysql/cj/protocol/ResultsetRow
instanceKlass com/mysql/cj/protocol/a/ColumnDefinitionReader
instanceKlass com/mysql/cj/protocol/ProtocolEntityReader
instanceKlass  @bci com/mysql/cj/protocol/a/NativeAuthenticationProvider <init> ()V 26 <appendix> member <vmtarget> ; # com/mysql/cj/protocol/a/NativeAuthenticationProvider$$Lambda+0x0000019a25189178
instanceKlass com/mysql/cj/callback/MysqlCallback
instanceKlass com/mysql/cj/callback/MysqlCallbackHandler
instanceKlass com/mysql/cj/protocol/a/NativeAuthenticationProvider
instanceKlass com/mysql/cj/protocol/a/SimplePacketReader
instanceKlass com/mysql/cj/protocol/a/SimplePacketSender
instanceKlass com/mysql/cj/protocol/a/NativePacketPayload
instanceKlass com/mysql/cj/log/BaseMetricsHolder
instanceKlass com/mysql/cj/protocol/AbstractProtocol$2
instanceKlass com/mysql/cj/protocol/AbstractProtocol$1
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 504 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a251877d0
instanceKlass java/time/ZonedDateTime
instanceKlass java/time/chrono/ChronoZonedDateTime
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 487 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25187298
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 470 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25186d60
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 453 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25186828
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 436 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a251862f0
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 419 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a251860c0
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 402 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25185e90
instanceKlass java/time/OffsetTime
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 385 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25185958
instanceKlass java/time/OffsetDateTime
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 368 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25185420
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 351 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a251851f0
instanceKlass java/time/LocalTime
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 334 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25184cb8
instanceKlass java/time/LocalDateTime
instanceKlass java/time/chrono/ChronoLocalDateTime
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 317 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25184780
instanceKlass java/time/LocalDate
instanceKlass java/time/chrono/ChronoLocalDate
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 300 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25184248
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 284 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25184018
instanceKlass java/time/Instant
instanceKlass java/time/temporal/TemporalAdjuster
instanceKlass java/time/temporal/Temporal
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 267 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25183ae0
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 250 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a251838b0
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 233 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25183680
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 216 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25183148
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 199 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25182f18
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 182 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a251829e0
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 165 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a251824a8
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 148 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25181c58
instanceKlass java/util/Calendar
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 131 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25181720
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 114 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a251811e8
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 97 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25180fb8
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 81 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25180a80
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 64 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25180230
instanceKlass  @bci com/mysql/cj/protocol/a/NativeProtocol <clinit> ()V 47 <appendix> argL0 ; # com/mysql/cj/protocol/a/NativeProtocol$$Lambda+0x0000019a25180000
instanceKlass com/mysql/cj/protocol/a/AbstractValueEncoder
instanceKlass com/mysql/cj/protocol/ValueEncoder
instanceKlass com/mysql/cj/protocol/ServerSessionStateController$ServerSessionStateChanges
instanceKlass com/mysql/cj/protocol/MessageHeader
instanceKlass com/mysql/cj/protocol/ServerCapabilities
instanceKlass com/mysql/cj/CharsetSettings
instanceKlass com/mysql/cj/MessageBuilder
instanceKlass com/mysql/cj/protocol/AuthenticationProvider
instanceKlass com/mysql/cj/protocol/ServerSession
instanceKlass com/mysql/cj/protocol/MessageReader
instanceKlass com/mysql/cj/protocol/MessageSender
instanceKlass com/mysql/cj/protocol/PacketReceivedTimeHolder
instanceKlass com/mysql/cj/protocol/AbstractProtocol
instanceKlass com/mysql/cj/protocol/Protocol$ProtocolEventHandler
instanceKlass com/mysql/cj/conf/RuntimeProperty$RuntimePropertyListener
instanceKlass java/util/concurrent/atomic/Striped64$1
instanceKlass jdk/internal/util/random/RandomSupport
instanceKlass java/net/InetAddress$CachedLookup
instanceKlass sun/net/InetAddressCachePolicy$1
instanceKlass sun/net/InetAddressCachePolicy
instanceKlass java/util/stream/Nodes$ArrayNode
instanceKlass  @bci java/net/InetAddress getAddressesFromNameService (Ljava/lang/String;)[Ljava/net/InetAddress; 94 <appendix> argL0 ; # java/net/InetAddress$$Lambda+0x0000019a25144a00
instanceKlass  @bci java/net/InetAddress loadResolver ()Ljava/net/spi/InetAddressResolver; 8 <appendix> argL0 ; # java/net/InetAddress$$Lambda+0x0000019a251447b0
instanceKlass java/net/spi/InetAddressResolverProvider
instanceKlass java/net/InetAddress$NameServiceAddresses
instanceKlass java/net/InetAddress$Addresses
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Iter
instanceKlass com/mysql/cj/protocol/StandardSocketFactory
instanceKlass com/mysql/cj/protocol/PacketSentTimeHolder
instanceKlass com/mysql/cj/protocol/SocketFactory
instanceKlass com/mysql/cj/protocol/SocketMetadata
instanceKlass com/mysql/cj/protocol/AbstractSocketConnection
instanceKlass  @bci com/mysql/cj/jdbc/ConnectionImpl initializeSafeQueryInterceptors ()V 45 <appendix> argL0 ; # com/mysql/cj/jdbc/ConnectionImpl$$Lambda+0x0000019a25137c08
instanceKlass  @bci com/mysql/cj/jdbc/ConnectionImpl initializeSafeQueryInterceptors ()V 35 <appendix> member <vmtarget> ; # com/mysql/cj/jdbc/ConnectionImpl$$Lambda+0x0000019a251379b0
instanceKlass com/mysql/cj/NoSubInterceptorWrapper
instanceKlass java/util/LinkedList$LLSpliterator
instanceKlass  @bci java/util/stream/Collectors toCollection (Ljava/util/function/Supplier;)Ljava/util/stream/Collector; 10 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x0000019a25142af0
instanceKlass  @bci java/util/stream/Collectors toCollection (Ljava/util/function/Supplier;)Ljava/util/stream/Collector; 5 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x0000019a251428b0
instanceKlass  @bci com/mysql/cj/util/Util loadClasses (Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;Lcom/mysql/cj/exceptions/ExceptionInterceptor;)Ljava/util/List; 34 <appendix> argL0 ; # com/mysql/cj/util/Util$$Lambda+0x0000019a251374d8
instanceKlass  @bci com/mysql/cj/util/Util loadClasses (Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;Lcom/mysql/cj/exceptions/ExceptionInterceptor;)Ljava/util/List; 24 <appendix> member <vmtarget> ; # com/mysql/cj/util/Util$$Lambda+0x0000019a25137280
instanceKlass  @bci com/mysql/cj/util/Util loadClasses (Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;Lcom/mysql/cj/exceptions/ExceptionInterceptor;)Ljava/util/List; 12 <appendix> argL0 ; # com/mysql/cj/util/Util$$Lambda+0x0000019a25137020
instanceKlass com/mysql/cj/interceptors/QueryInterceptor
instanceKlass com/mysql/cj/result/Row
instanceKlass com/mysql/cj/jdbc/DatabaseMetaData$IteratorWithCleanup
instanceKlass com/mysql/cj/protocol/ResultsetRows
instanceKlass com/mysql/cj/result/RowList
instanceKlass com/mysql/cj/jdbc/DatabaseMetaData
instanceKlass com/mysql/cj/log/LogFactory
instanceKlass com/mysql/cj/log/NullLogger
instanceKlass com/mysql/cj/result/ValueFactory
instanceKlass com/mysql/cj/protocol/Message
instanceKlass com/mysql/cj/protocol/Protocol
instanceKlass com/mysql/cj/protocol/SocketConnection
instanceKlass com/mysql/cj/DataStoreMetadata
instanceKlass com/mysql/cj/CoreSession
instanceKlass com/mysql/cj/protocol/a/result/NativeResultset
instanceKlass com/mysql/cj/WarningListener
instanceKlass com/mysql/cj/jdbc/result/ResultSetInternalMethods
instanceKlass com/mysql/cj/protocol/Resultset
instanceKlass com/mysql/cj/protocol/ResultsetRowsOwner
instanceKlass com/mysql/cj/jdbc/result/ResultSetFactory
instanceKlass com/mysql/cj/conf/AbstractRuntimeProperty
instanceKlass com/mysql/cj/PerConnectionLRUFactory
instanceKlass com/mysql/cj/util/PerVmServerConfigCacheFactory
instanceKlass com/mysql/cj/CacheAdapterFactory
instanceKlass java/text/AttributedCharacterIterator$Attribute
instanceKlass com/mysql/cj/conf/AbstractPropertyDefinition
instanceKlass com/mysql/cj/conf/PropertyDefinitions
instanceKlass com/mysql/cj/conf/RuntimeProperty
instanceKlass com/mysql/cj/conf/PropertyDefinition
instanceKlass com/mysql/cj/conf/DefaultPropertySet
instanceKlass  @bci com/mysql/cj/conf/HostInfo exposeAsProperties ()Ljava/util/Properties; 23 <appendix> member <vmtarget> ; # com/mysql/cj/conf/HostInfo$$Lambda+0x0000019a25125048
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper newResourceBundle (Ljava/lang/Class;)Ljava/util/ResourceBundle; 22 <bsm> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a25128000
instanceKlass java/lang/invoke/MethodHandle$1
instanceKlass com/mysql/cj/log/StandardLogger
instanceKlass com/mysql/cj/log/Log
instanceKlass com/mysql/cj/Session
instanceKlass com/mysql/cj/jdbc/result/CachedResultSetMetaData
instanceKlass com/mysql/cj/protocol/ColumnDefinition
instanceKlass com/mysql/cj/protocol/ProtocolEntity
instanceKlass java/sql/NClob
instanceKlass java/sql/Savepoint
instanceKlass jdk/internal/foreign/MemorySessionImpl
instanceKlass java/lang/foreign/MemorySegment$Scope
instanceKlass java/sql/SQLXML
instanceKlass java/sql/CallableStatement
instanceKlass com/mysql/cj/jdbc/StatementImpl
instanceKlass com/mysql/cj/jdbc/JdbcPreparedStatement
instanceKlass com/mysql/cj/jdbc/JdbcStatement
instanceKlass com/mysql/cj/Query
instanceKlass java/sql/PreparedStatement
instanceKlass java/sql/DatabaseMetaData
instanceKlass com/mysql/cj/protocol/ProtocolEntityFactory
instanceKlass com/mysql/cj/jdbc/IterateBlock
instanceKlass com/mysql/cj/exceptions/ExceptionInterceptor
instanceKlass com/mysql/cj/jdbc/ConnectionImpl
instanceKlass com/mysql/cj/Session$SessionEventListener
instanceKlass com/mysql/cj/jdbc/JdbcConnection
instanceKlass com/mysql/cj/TransactionEventHandler
instanceKlass com/mysql/cj/MysqlConnection
instanceKlass com/mysql/cj/jdbc/NonRegisteringDriver$1
instanceKlass  @bci com/mysql/cj/conf/ConnectionUrl fixHostInfo (Lcom/mysql/cj/conf/HostInfo;)Lcom/mysql/cj/conf/HostInfo; 33 <appendix> member <vmtarget> ; # com/mysql/cj/conf/ConnectionUrl$$Lambda+0x0000019a2511bcf0
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass  @bci com/mysql/cj/conf/ConnectionUrl collectHostsInfo (Lcom/mysql/cj/conf/ConnectionUrlParser;)V 29 <appendix> member <vmtarget> ; # com/mysql/cj/conf/ConnectionUrl$$Lambda+0x0000019a2511baa8
instanceKlass  @bci com/mysql/cj/conf/ConnectionUrl collectHostsInfo (Lcom/mysql/cj/conf/ConnectionUrlParser;)V 10 <appendix> member <vmtarget> ; # com/mysql/cj/conf/ConnectionUrl$$Lambda+0x0000019a2511b850
instanceKlass  @cpi com/mysql/cj/conf/ConnectionUrl 492 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a2511cc00
instanceKlass  @bci com/mysql/cj/conf/ConnectionUrl collectProperties (Lcom/mysql/cj/conf/ConnectionUrlParser;Ljava/util/Properties;)V 40 <appendix> member <vmtarget> ; # com/mysql/cj/conf/ConnectionUrl$$Lambda+0x0000019a2511b608
instanceKlass  @bci java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet entryConsumer (Ljava/util/function/Consumer;)Ljava/util/function/Consumer; 1 <appendix> member <vmtarget> ; # java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$$Lambda+0x0000019a25141a98
instanceKlass  @cpi com/mysql/cj/conf/HostInfo 158 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a2511c800
instanceKlass  @bci com/mysql/cj/conf/ConnectionUrl collectProperties (Lcom/mysql/cj/conf/ConnectionUrlParser;Ljava/util/Properties;)V 15 <appendix> member <vmtarget> ; # com/mysql/cj/conf/ConnectionUrl$$Lambda+0x0000019a2511b3c0
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntrySetSpliterator
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2511c400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2511c000
instanceKlass java/math/MathContext
instanceKlass java/text/FieldPosition
instanceKlass java/text/Format
instanceKlass java/util/ResourceBundle$Control$2
instanceKlass sun/util/resources/Bundles
instanceKlass  @bci java/util/ResourceBundle getLoader (Ljava/lang/Module;)Ljava/lang/ClassLoader; 6 <appendix> member <vmtarget> ; # java/util/ResourceBundle$$Lambda+0x0000019a250fee40
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass java/util/ResourceBundle$CacheKeyReference
instanceKlass java/util/ResourceBundle$CacheKey
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass java/util/ResourceBundle$Control
instanceKlass java/util/stream/AbstractSpinedBuffer
instanceKlass java/util/stream/Node$Builder
instanceKlass java/util/stream/Node$OfDouble
instanceKlass java/util/stream/Node$OfLong
instanceKlass java/util/stream/Node$OfInt
instanceKlass java/util/stream/Node$OfPrimitive
instanceKlass java/util/stream/Nodes$EmptyNode
instanceKlass java/util/stream/Node
instanceKlass java/util/stream/Nodes
instanceKlass  @bci java/util/stream/ReferencePipeline toArray ()[Ljava/lang/Object; 1 <appendix> argL0 ; # java/util/stream/ReferencePipeline$$Lambda+0x0000019a250fdc70
instanceKlass java/util/function/IntFunction
instanceKlass java/util/ImmutableCollections$Access$1
instanceKlass jdk/internal/access/JavaUtilCollectionAccess
instanceKlass java/util/ImmutableCollections$Access
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleControlProviderHolder lambda$static$0 ()Ljava/util/List; 11 <appendix> argL0 ; # java/util/ResourceBundle$ResourceBundleControlProviderHolder$$Lambda+0x0000019a250fd3b0
instanceKlass java/util/ServiceLoader$ProviderSpliterator
instanceKlass java/util/spi/ResourceBundleControlProvider
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleControlProviderHolder <clinit> ()V 0 <appendix> argL0 ; # java/util/ResourceBundle$ResourceBundleControlProviderHolder$$Lambda+0x0000019a250fccc0
instanceKlass java/util/ResourceBundle$ResourceBundleControlProviderHolder
instanceKlass java/util/ResourceBundle$1
instanceKlass jdk/internal/access/JavaUtilResourceBundleAccess
instanceKlass java/util/ResourceBundle
instanceKlass com/mysql/cj/Messages
instanceKlass com/mysql/cj/Constants
instanceKlass com/mysql/cj/util/Util
instanceKlass com/mysql/cj/conf/HostInfo
instanceKlass com/mysql/cj/conf/ConnectionUrlParser$Pair
instanceKlass com/mysql/cj/util/StringInspector
instanceKlass  @bci com/mysql/cj/conf/ConnectionUrl buildConnectionStringCacheKey (Ljava/lang/String;Ljava/util/Properties;)Ljava/lang/String; 35 <appendix> member <vmtarget> ; # com/mysql/cj/conf/ConnectionUrl$$Lambda+0x0000019a25119c38
instanceKlass java/net/URLDecoder
instanceKlass  @bci java/util/regex/Pattern negate (Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$CharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000031
instanceKlass  @bci java/util/regex/Pattern union (Ljava/util/regex/Pattern$CharPredicate;Ljava/util/regex/Pattern$CharPredicate;Z)Ljava/util/regex/Pattern$CharPredicate; 6 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000032
instanceKlass com/mysql/cj/conf/ConnectionUrlParser
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass com/mysql/cj/conf/ConnectionUrl
instanceKlass com/mysql/cj/conf/DatabaseUrlContainer
instanceKlass com/mysql/cj/util/StringUtils
instanceKlass  @bci java/util/stream/Collectors castingIdentity ()Ljava/util/function/Function; 0 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000042
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x0000019a250f9e60
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x0000019a250f9c20
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x0000019a250f99f0
instanceKlass  @bci com/intellij/database/remote/jdbc/helpers/JdbcHelperDetection getImplsFor (Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/Iterable; 124 <appendix> argL0 ; # com/intellij/database/remote/jdbc/helpers/JdbcHelperDetection$$Lambda+0x0000019a25114148
instanceKlass  @bci com/intellij/database/remote/jdbc/helpers/JdbcHelperDetection getImplsFor (Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/Iterable; 110 <appendix> argL0 ; # com/intellij/database/remote/jdbc/helpers/JdbcHelperDetection$$Lambda+0x0000019a25113ea0
instanceKlass com/intellij/database/remote/jdbc/RemoteCloseable
instanceKlass com/intellij/database/remote/jdbc/ColumnInfo
instanceKlass com/intellij/database/remote/jdbc/RemotePreparedStatement
instanceKlass com/intellij/database/remote/jdbc/RemoteStatement
instanceKlass java/sql/ResultSetMetaData
instanceKlass com/intellij/database/remote/jdbc/RemoteResultSet$DataRetrievingOptions
instanceKlass java/sql/ResultSet
instanceKlass java/sql/Statement
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelper$Type
instanceKlass com/intellij/database/remote/jdba/jdbc/JdbcConnectionProvider
instanceKlass it/unimi/dsi/fastutil/ints/IntArrays$ArrayHashStrategy
instanceKlass it/unimi/dsi/fastutil/ints/IntArrays$Segment
instanceKlass it/unimi/dsi/fastutil/ints/IntArrays
instanceKlass it/unimi/dsi/fastutil/ints/IntListIterator
instanceKlass it/unimi/dsi/fastutil/ints/IntBidirectionalIterator
instanceKlass it/unimi/dsi/fastutil/objects/ObjectBidirectionalIterator
instanceKlass it/unimi/dsi/fastutil/BidirectionalIterator
instanceKlass java/util/function/IntUnaryOperator
instanceKlass it/unimi/dsi/fastutil/ints/IntStack
instanceKlass it/unimi/dsi/fastutil/Stack
instanceKlass it/unimi/dsi/fastutil/ints/IntSpliterator
instanceKlass it/unimi/dsi/fastutil/ints/IntIterator
instanceKlass java/util/PrimitiveIterator$OfInt
instanceKlass java/util/PrimitiveIterator
instanceKlass java/util/function/IntPredicate
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelperDetection$1
instanceKlass it/unimi/dsi/fastutil/ints/IntList
instanceKlass it/unimi/dsi/fastutil/ints/IntSet
instanceKlass it/unimi/dsi/fastutil/ints/IntCollection
instanceKlass it/unimi/dsi/fastutil/ints/IntIterable
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelperDetection$CompleteOrderBuilder
instanceKlass it/unimi/dsi/fastutil/objects/ObjectOpenCustomHashSet$SetIterator
instanceKlass it/unimi/dsi/fastutil/HashCommon
instanceKlass com/intellij/database/remote/jdbc/helpers/PgBaseJdbcHelper$EnglishCaseInsensitiveStringHashingStrategy
instanceKlass it/unimi/dsi/fastutil/objects/ObjectSpliterator
instanceKlass it/unimi/dsi/fastutil/objects/ObjectIterator
instanceKlass it/unimi/dsi/fastutil/objects/ObjectSet
instanceKlass it/unimi/dsi/fastutil/objects/ObjectCollection
instanceKlass it/unimi/dsi/fastutil/objects/ObjectIterable
instanceKlass it/unimi/dsi/fastutil/Hash
instanceKlass it/unimi/dsi/fastutil/Hash$Strategy
instanceKlass com/intellij/openapi/util/Pair
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl$PropertyReplacement
instanceKlass  @bci java/util/regex/CharPredicates ASCII_SPACE ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000026
instanceKlass  @bci java/util/regex/CharPredicates ASCII_WORD ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x0000019a250f7d68
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl$6
instanceKlass java/time/temporal/TemporalQueries$7
instanceKlass java/time/temporal/TemporalQueries$6
instanceKlass java/time/temporal/TemporalQueries$5
instanceKlass java/time/temporal/TemporalQueries$4
instanceKlass java/time/temporal/TemporalQueries$3
instanceKlass java/time/temporal/TemporalQueries$2
instanceKlass java/time/temporal/TemporalQueries$1
instanceKlass java/time/temporal/TemporalQueries
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl$5
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl$4
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl$3
instanceKlass  @bci java/time/format/DateTimeFormatter <clinit> ()V 1075 <appendix> argL0 ; # java/time/format/DateTimeFormatter$$Lambda+0x80000000e
instanceKlass  @bci java/time/format/DateTimeFormatter <clinit> ()V 1067 <appendix> argL0 ; # java/time/format/DateTimeFormatter$$Lambda+0x80000000d
instanceKlass java/time/Period
instanceKlass java/time/chrono/ChronoPeriod
instanceKlass java/time/format/DateTimeFormatterBuilder$TextPrinterParser
instanceKlass java/time/format/DateTimeTextProvider$1
instanceKlass java/time/format/DateTimeTextProvider
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass java/time/format/DateTimeTextProvider$LocaleStore
instanceKlass java/time/format/DateTimeFormatterBuilder$InstantPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$StringLiteralPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$ZoneIdPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$OffsetIdPrinterParser
instanceKlass java/time/format/DecimalStyle
instanceKlass java/time/chrono/AbstractChronology
instanceKlass java/time/chrono/Chronology
instanceKlass java/time/format/DateTimeFormatter
instanceKlass java/time/format/DateTimeFormatterBuilder$CompositePrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$CharLiteralPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$NumberPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$DateTimePrinterParser
instanceKlass java/time/temporal/JulianFields
instanceKlass java/time/temporal/IsoFields
instanceKlass java/time/temporal/ValueRange
instanceKlass java/time/Duration
instanceKlass java/time/temporal/TemporalAmount
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass  @bci java/time/format/DateTimeFormatterBuilder <clinit> ()V 0 <appendix> argL0 ; # java/time/format/DateTimeFormatterBuilder$$Lambda+0x80000000f
instanceKlass java/time/ZoneId
instanceKlass java/time/temporal/TemporalAccessor
instanceKlass java/time/temporal/TemporalQuery
instanceKlass java/time/format/DateTimeFormatterBuilder
instanceKlass java/time/temporal/TemporalField
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl$2
instanceKlass java/sql/Clob
instanceKlass java/sql/Blob
instanceKlass com/intellij/database/remote/jdba/jdbc/JdbcIntermediateFacade
instanceKlass com/intellij/database/remote/jdba/intermediate/PrimeIntermediateFacade
instanceKlass com/intellij/database/remote/jdba/core/ImplementationAccessibleService
instanceKlass com/intellij/database/remote/jdba/intermediate/DBExceptionRecognizer
instanceKlass java/util/Date
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl$TemporalGetter
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelper
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelperDetection
instanceKlass com/intellij/database/remote/jdbc/TypedKey
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcSettings
instanceKlass  @bci sun/rmi/transport/DGCClient$EndpointEntry$RenewCleanThread run ()V 236 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000019a250b0800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a250b0400
instanceKlass  @bci jdk/internal/reflect/DirectMethodHandleAccessor invokeImpl (Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; 92 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000019a250b0000
instanceKlass  @bci sun/rmi/transport/DGCClient$EndpointEntry$RenewCleanThread run ()V 236 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a250afc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250af800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250af400
instanceKlass  @bci sun/rmi/transport/DGCClient$EndpointEntry$RenewCleanThread run ()V 236 <appendix> member <vmtarget> ; # sun/rmi/transport/DGCClient$EndpointEntry$RenewCleanThread$$Lambda+0x0000019a250f4c68
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250af000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250aec00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a250ae800
instanceKlass java/util/HashMap$UnsafeHolder
instanceKlass  @cpi sun/rmi/transport/DGCClient$EndpointEntry$RenewCleanThread 148 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a250adc00
instanceKlass java/awt/Desktop$DesktopActions
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250ad000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250acc00
instanceKlass com/jetbrains/exported/JBRApi$Provided
instanceKlass com/intellij/database/remote/toolkit/JbrApiHandler$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250ac800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250ac400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250ac000
instanceKlass sun/rmi/transport/DGCClient$EndpointEntry$1
instanceKlass sun/rmi/transport/tcp/TCPChannel$1
instanceKlass  @bci sun/rmi/transport/StreamRemoteCall getInputStream ()Ljava/io/ObjectInput; 46 <appendix> member <vmtarget> ; # sun/rmi/transport/StreamRemoteCall$$Lambda+0x0000019a250f3430
instanceKlass  @bci sun/rmi/transport/DGCImpl_Stub dirty ([Ljava/rmi/server/ObjID;JLjava/rmi/dgc/Lease;)Ljava/rmi/dgc/Lease; 24 <appendix> argL0 ; # sun/rmi/transport/DGCImpl_Stub$$Lambda+0x0000019a250f3200
instanceKlass sun/net/spi/DefaultProxySelector$3
instanceKlass sun/net/spi/DefaultProxySelector$NonProxyInfo
instanceKlass sun/net/NetProperties$1
instanceKlass sun/net/NetProperties
instanceKlass sun/net/spi/DefaultProxySelector$1
instanceKlass java/net/Proxy
instanceKlass java/net/ProxySelector
instanceKlass java/net/SocksSocketImpl$3
instanceKlass java/net/SocksConsts
instanceKlass sun/rmi/transport/DGCClient$EndpointEntry$RefEntry
instanceKlass sun/rmi/transport/DGCClient$EndpointEntry$RenewCleanThread
instanceKlass sun/rmi/transport/DGCClient$EndpointEntry
instanceKlass  @bci sun/rmi/transport/DGCClient <clinit> ()V 50 <appendix> argL0 ; # sun/rmi/transport/DGCClient$$Lambda+0x0000019a250f0628
instanceKlass  @bci sun/rmi/transport/DGCClient <clinit> ()V 33 <appendix> argL0 ; # sun/rmi/transport/DGCClient$$Lambda+0x0000019a250f03f8
instanceKlass  @bci sun/rmi/transport/DGCClient <clinit> ()V 16 <appendix> argL0 ; # sun/rmi/transport/DGCClient$$Lambda+0x0000019a250f01c8
instanceKlass sun/rmi/transport/DGCClient
instanceKlass sun/reflect/misc/ReflectUtil
instanceKlass java/io/ObjectInputStream$Caches
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250ab000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250aac00
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x0000019a250aa800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a250aa400
instanceKlass java/io/ObjectStreamClass$5
instanceKlass java/io/ObjectStreamClass$4
instanceKlass java/io/ObjectStreamClass$3
instanceKlass java/io/ObjectStreamClass$MemberSignature
instanceKlass java/io/ObjectStreamClass$1
instanceKlass sun/rmi/transport/SequenceEntry
instanceKlass sun/rmi/transport/DGCImpl$1
instanceKlass sun/rmi/transport/DGCImpl$LeaseInfo
instanceKlass java/rmi/dgc/VMID
instanceKlass java/rmi/dgc/Lease
instanceKlass java/io/ObjectInputStream$GetField
instanceKlass java/io/ObjectStreamClass$ExceptionInfo
instanceKlass java/io/ObjectInputStream$Logging
instanceKlass java/io/ObjectInputStream$FilterValues
instanceKlass java/util/concurrent/Executors$RunnableAdapter
instanceKlass java/util/concurrent/Callable
instanceKlass java/util/concurrent/FutureTask$WaitNode
instanceKlass java/util/concurrent/FutureTask
instanceKlass sun/rmi/transport/DGCAckHandler$1
instanceKlass  @bci sun/rmi/transport/DGCAckHandler <clinit> ()V 16 <appendix> argL0 ; # sun/rmi/transport/DGCAckHandler$$Lambda+0x0000019a250eb508
instanceKlass sun/rmi/transport/DGCAckHandler
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a8400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a8000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a7c00
instanceKlass  @cpi com/mysql/cj/protocol/a/authentication/AuthenticationKerberosClient 226 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a250a7800
instanceKlass java/io/SerialCallbackContext
instanceKlass java/io/ObjectStreamClass$ClassDataSlot
instanceKlass  @bci sun/rmi/server/LoaderHandler <clinit> ()V 36 <appendix> argL0 ; # sun/rmi/server/LoaderHandler$$Lambda+0x0000019a250ea308
instanceKlass  @bci sun/rmi/server/LoaderHandler <clinit> ()V 0 <appendix> argL0 ; # sun/rmi/server/LoaderHandler$$Lambda+0x0000019a250ea0d8
instanceKlass sun/rmi/server/LoaderHandler
instanceKlass java/rmi/server/RMIClassLoader$1
instanceKlass java/rmi/server/RMIClassLoaderSpi
instanceKlass java/rmi/server/RMIClassLoader
instanceKlass java/io/ObjectStreamClass$FieldReflector
# instanceKlass java/util/stream/Collectors$$Lambda+0x0000019a250e8f80
instanceKlass java/io/ObjectStreamClass$FieldReflectorKey
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 22 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000043
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 17 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000041
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 12 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x80000003e
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 7 <appendix> member <vmtarget> ; # java/util/stream/Collectors$$Lambda+0x800000046
instanceKlass  @bci java/lang/Class methodToString (Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/String; 42 <appendix> argL0 ; # java/lang/Class$$Lambda+0x0000019a250e8840
instanceKlass jdk/internal/reflect/ClassDefiner$1
instanceKlass jdk/internal/reflect/ClassDefiner
instanceKlass jdk/internal/reflect/MethodAccessorGenerator$1
instanceKlass jdk/internal/reflect/Label$PatchInfo
instanceKlass jdk/internal/reflect/Label
instanceKlass jdk/internal/reflect/UTF8
instanceKlass jdk/internal/reflect/ClassFileAssembler
instanceKlass jdk/internal/reflect/ByteVectorImpl
instanceKlass jdk/internal/reflect/ByteVector
instanceKlass jdk/internal/reflect/ByteVectorFactory
instanceKlass jdk/internal/reflect/AccessorGenerator
instanceKlass jdk/internal/reflect/ClassFileConstants
instanceKlass java/util/ComparableTimSort
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a6000
instanceKlass  @bci jdk/internal/reflect/MethodHandleLongFieldAccessorImpl getLong (Ljava/lang/Object;)J 11 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000019a250a5c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a250a5800
instanceKlass java/io/ObjectStreamClass$2
instanceKlass java/io/ClassCache
instanceKlass java/io/ObjectStreamClass$Caches
instanceKlass java/io/ObjectStreamClass
instanceKlass sun/rmi/server/MarshalOutputStream$1
instanceKlass java/io/ObjectOutputStream$ReplaceTable
instanceKlass java/io/ObjectOutputStream$HandleTable
instanceKlass java/io/ObjectOutput
instanceKlass  @bci sun/rmi/server/UnicastServerRef unmarshalCustomCallData (Ljava/io/ObjectInput;)V 21 <appendix> member <vmtarget> ; # sun/rmi/server/UnicastServerRef$$Lambda+0x0000019a250e3598
instanceKlass sun/rmi/transport/Transport$1
instanceKlass  @bci sun/rmi/transport/Transport setContextClassLoader (Ljava/lang/ClassLoader;)V 1 <appendix> member <vmtarget> ; # sun/rmi/transport/Transport$$Lambda+0x0000019a250e3118
# instanceKlass sun/rmi/transport/Transport$$Lambda+0x0000019a250e2ee0
instanceKlass  @bci java/io/ObjectInputFilter$Config <clinit> ()V 368 <appendix> argL0 ; # java/io/ObjectInputFilter$Config$$Lambda+0x0000019a250e2cb0
instanceKlass jdk/internal/access/JavaObjectInputFilterAccess
instanceKlass java/io/ObjectInputFilter$Config$BuiltinFilterFactory
instanceKlass  @bci java/io/ObjectInputFilter$Config <clinit> ()V 80 <appendix> argL0 ; # java/io/ObjectInputFilter$Config$$Lambda+0x0000019a250e2618
instanceKlass  @bci java/io/ObjectInputFilter$Config <clinit> ()V 56 <appendix> argL0 ; # java/io/ObjectInputFilter$Config$$Lambda+0x0000019a250e23e8
instanceKlass jdk/internal/logger/LoggerFinderLoader
instanceKlass  @bci java/lang/System$LoggerFinder accessProvider ()Ljava/lang/System$LoggerFinder; 8 <appendix> argL0 ; # java/lang/System$LoggerFinder$$Lambda+0x0000019a250e1a30
instanceKlass jdk/internal/logger/LazyLoggers$LazyLoggerFactories
instanceKlass jdk/internal/logger/LazyLoggers$1
instanceKlass jdk/internal/logger/LazyLoggers
instanceKlass java/io/ObjectInputFilter$Config
instanceKlass java/io/ObjectInputStream$ValidationList
instanceKlass java/io/ObjectInputStream$HandleTable$HandleList
instanceKlass java/io/ObjectInputStream$HandleTable
instanceKlass  @bci sun/rmi/server/MarshalInputStream <clinit> ()V 0 <appendix> argL0 ; # sun/rmi/server/MarshalInputStream$$Lambda+0x0000019a250dffa8
instanceKlass  @bci java/io/ObjectInputStream <clinit> ()V 100 <appendix> argL0 ; # java/io/ObjectInputStream$$Lambda+0x0000019a250dfd78
instanceKlass jdk/internal/access/JavaObjectInputStreamReadString
instanceKlass  @bci java/io/ObjectInputStream <clinit> ()V 92 <appendix> argL0 ; # java/io/ObjectInputStream$$Lambda+0x0000019a250df938
instanceKlass  @cpi java/io/ObjectInputStream 1214 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a250a5400
instanceKlass jdk/internal/access/JavaObjectInputStreamAccess
instanceKlass java/io/ObjectStreamConstants
instanceKlass java/io/ObjectInput
instanceKlass sun/rmi/transport/StreamRemoteCall
instanceKlass java/rmi/server/RemoteCall
instanceKlass sun/rmi/transport/tcp/TCPConnection
instanceKlass sun/rmi/transport/Connection
instanceKlass  @bci sun/rmi/transport/tcp/TCPChannel <clinit> ()V 34 <appendix> argL0 ; # sun/rmi/transport/tcp/TCPChannel$$Lambda+0x0000019a250dd960
instanceKlass  @bci sun/rmi/transport/tcp/TCPChannel <clinit> ()V 17 <appendix> argL0 ; # sun/rmi/transport/tcp/TCPChannel$$Lambda+0x0000019a250dd730
instanceKlass  @bci sun/rmi/transport/tcp/TCPChannel <clinit> ()V 0 <appendix> argL0 ; # sun/rmi/transport/tcp/TCPChannel$$Lambda+0x0000019a250dd500
instanceKlass sun/rmi/transport/tcp/TCPChannel
instanceKlass sun/rmi/transport/Channel
instanceKlass sun/nio/ch/Util$BufferCache
instanceKlass sun/nio/ch/Util
instanceKlass sun/nio/ch/ExtendedSocketOption$1
instanceKlass sun/nio/ch/ExtendedSocketOption
instanceKlass sun/nio/ch/OptionKey
instanceKlass sun/nio/ch/SocketOptionRegistry$LazyInitialization
instanceKlass sun/nio/ch/SocketOptionRegistry$RegistryKey
instanceKlass sun/nio/ch/SocketOptionRegistry
instanceKlass java/net/StandardSocketOptions$StdSocketOption
instanceKlass java/net/StandardSocketOptions
instanceKlass  @bci sun/rmi/transport/tcp/TCPTransport$ConnectionHandler run ()V 29 <appendix> member <vmtarget> ; # sun/rmi/transport/tcp/TCPTransport$ConnectionHandler$$Lambda+0x0000019a250da540
instanceKlass  @bci sun/rmi/transport/tcp/TCPTransport$ConnectionHandler run ()V 20 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a250a5000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a4c00
instanceKlass  @bci sun/rmi/transport/tcp/TCPTransport$ConnectionHandler run ()V 20 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a250a4800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a4400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a4000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a3c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a3800
instanceKlass  @bci sun/rmi/transport/tcp/TCPTransport$ConnectionHandler run ()V 20 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a250a3400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a3000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a2c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a2800
instanceKlass java/util/concurrent/ForkJoinPool$2
instanceKlass jdk/internal/access/JavaUtilConcurrentFJPAccess
instanceKlass java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory
instanceKlass java/util/concurrent/ForkJoinTask
instanceKlass java/util/concurrent/ForkJoinPool$WorkQueue
instanceKlass java/util/concurrent/CountDownLatch
instanceKlass java/util/concurrent/ForkJoinPool$ForkJoinWorkerThreadFactory
instanceKlass sun/rmi/transport/tcp/TCPTransport$ConnectionHandler
instanceKlass sun/nio/ch/IOStatus
instanceKlass sun/nio/ch/NativeThread
instanceKlass java/util/regex/ASCII
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass  @bci sun/rmi/server/UnicastRef remoteToString ()Ljava/lang/String; 14 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a250a2400
instanceKlass java/net/Socket
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a2000
instanceKlass  @bci sun/rmi/server/UnicastRef remoteToString ()Ljava/lang/String; 14 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a250a1c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a1800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a1400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a1000
instanceKlass  @bci java/rmi/server/ObjID toString ()Ljava/lang/String; 34 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000019a250a0c00
instanceKlass  @bci java/rmi/server/ObjID toString ()Ljava/lang/String; 34 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a250a0800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a250a0400
instanceKlass  @bci java/rmi/server/ObjID toString ()Ljava/lang/String; 34 <appendix> form names 6 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a250a0000
instanceKlass  @bci java/rmi/server/ObjID toString ()Ljava/lang/String; 34 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2509fc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509f800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509f400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509f000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509ec00
instanceKlass  @bci java/rmi/server/ObjID toString ()Ljava/lang/String; 34 <appendix> form names 10 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a2509e800
instanceKlass  @bci java/rmi/server/ObjID toString ()Ljava/lang/String; 34 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2509e400
instanceKlass  @bci java/rmi/server/ObjID toString ()Ljava/lang/String; 34 <appendix> argL1 argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2509e000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509dc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509d800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509d400
instanceKlass  @bci sun/rmi/transport/LiveRef toString ()Ljava/lang/String; 31 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2509d000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509cc00
instanceKlass  @bci sun/rmi/transport/LiveRef toString ()Ljava/lang/String; 31 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2509c800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509c400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509c000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509bc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509b800
instanceKlass  @bci sun/rmi/transport/LiveRef toString ()Ljava/lang/String; 31 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2509b400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509b000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509ac00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2509a800
instanceKlass  @bci sun/rmi/transport/tcp/TCPEndpoint toString ()Ljava/lang/String; 56 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2509a400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a2509a000
instanceKlass  @bci sun/rmi/transport/tcp/TCPEndpoint toString ()Ljava/lang/String; 56 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25099c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25099800
instanceKlass  @bci sun/rmi/transport/tcp/TCPEndpoint toString ()Ljava/lang/String; 56 <appendix> form names 10 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a25099400
instanceKlass  @bci sun/rmi/transport/tcp/TCPEndpoint toString ()Ljava/lang/String; 56 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25099000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25098c00
instanceKlass  @bci sun/rmi/transport/tcp/TCPEndpoint toString ()Ljava/lang/String; 56 <appendix> argL3 argL2 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25098800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25098400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25098000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25097c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25097800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25097400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25097000
instanceKlass  @bci sun/rmi/transport/tcp/TCPEndpoint toString ()Ljava/lang/String; 56 <appendix> form names 14 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a25096c00
instanceKlass  @bci sun/rmi/transport/tcp/TCPEndpoint toString ()Ljava/lang/String; 56 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25096800
instanceKlass  @bci sun/rmi/transport/tcp/TCPEndpoint toString ()Ljava/lang/String; 56 <appendix> argL1 argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25096400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25096000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25095c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25095800
instanceKlass  @bci sun/rmi/transport/tcp/TCPEndpoint toString ()Ljava/lang/String; 56 <appendix> argL1 argL0 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25095400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25095000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25094c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25094800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25094400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25094000
instanceKlass sun/rmi/transport/GC$Daemon$1
instanceKlass sun/rmi/transport/GC$Daemon
instanceKlass sun/rmi/transport/GC$LatencyRequest
instanceKlass sun/rmi/transport/GC$1
instanceKlass sun/rmi/transport/GC$LatencyLock
instanceKlass sun/rmi/transport/GC
instanceKlass sun/rmi/transport/ObjectTable$Reaper
instanceKlass java/util/LinkedList$ListItr
instanceKlass java/util/Arrays$ArrayItr
instanceKlass  @bci java/lang/reflect/Proxy getProxyConstructor (Ljava/lang/Class;Ljava/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor; 80 <appendix> argL0 ; # java/lang/reflect/Proxy$$Lambda+0x0000019a250d79c0
instanceKlass sun/rmi/server/Util$1
instanceKlass java/sql/DriverPropertyInfo
instanceKlass com/intellij/database/remote/jdbc/RemoteConnection
instanceKlass  @bci java/rmi/server/ObjID useRandomIDs ()Z 0 <appendix> argL0 ; # java/rmi/server/ObjID$$Lambda+0x0000019a250d70c8
instanceKlass  @bci sun/rmi/server/Util getTypeDescriptor (Ljava/lang/Class;)Ljava/lang/String; 153 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25093400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25093000
instanceKlass  @bci sun/rmi/server/Util getTypeDescriptor (Ljava/lang/Class;)Ljava/lang/String; 153 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25092c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25092800
instanceKlass  @bci sun/rmi/runtime/RuntimeUtil$1 newThread (Ljava/lang/Runnable;)Ljava/lang/Thread; 12 <appendix> argL2 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25092400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25092000
instanceKlass  @bci sun/rmi/transport/tcp/TCPTransport$ConnectionHandler run ()V 20 <appendix> argL1 argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25091c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25091800
instanceKlass sun/rmi/server/UnicastServerRef$HashToMethod_Maps$1
instanceKlass sun/rmi/server/WeakClassHashMap$ValueCell
instanceKlass  @bci sun/rmi/runtime/RuntimeUtil$1 newThread (Ljava/lang/Runnable;)Ljava/lang/Thread; 12 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25091400
instanceKlass  @bci sun/rmi/runtime/RuntimeUtil$1 newThread (Ljava/lang/Runnable;)Ljava/lang/Thread; 12 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25091000
instanceKlass java/lang/Long$LongCache
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25090c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25090800
instanceKlass  @bci sun/rmi/transport/tcp/TCPTransport$ConnectionHandler run ()V 20 <appendix> argL1 argL0 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25090400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25090000
instanceKlass sun/rmi/transport/tcp/TCPTransport$AcceptLoop
instanceKlass sun/rmi/runtime/NewThreadAction$2
instanceKlass sun/rmi/runtime/NewThreadAction$1
instanceKlass sun/rmi/runtime/NewThreadAction
instanceKlass sun/net/NetHooks
instanceKlass  @bci sun/nio/ch/NioSocketImpl closerFor (Ljava/io/FileDescriptor;Z)Ljava/lang/Runnable; 5 <appendix> member <vmtarget> ; # sun/nio/ch/NioSocketImpl$$Lambda+0x0000019a250d4fd8
instanceKlass jdk/net/ExtendedSocketOptions$2
instanceKlass jdk/net/ExtendedSocketOptions$PlatformSocketOptions
instanceKlass jdk/net/ExtendedSocketOptions$ExtSocketOption
instanceKlass java/net/SocketOption
instanceKlass jdk/net/ExtendedSocketOptions
instanceKlass sun/net/ext/ExtendedSocketOptions
instanceKlass sun/nio/ch/Net$1
instanceKlass java/net/ProtocolFamily
instanceKlass sun/nio/ch/Net
instanceKlass java/net/InetSocketAddress$InetSocketAddressHolder
instanceKlass java/net/SocketAddress
instanceKlass sun/nio/ch/IOUtil
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass sun/net/PlatformSocketImpl
instanceKlass java/net/SocketImpl
instanceKlass java/net/SocketOptions
instanceKlass java/net/ServerSocket
instanceKlass sun/rmi/transport/ObjectEndpoint
instanceKlass sun/rmi/transport/DGCImpl$2$1
instanceKlass  @bci java/net/SocketPermissionCollection add (Ljava/security/Permission;)V 69 <appendix> member <vmtarget> ; # java/net/SocketPermissionCollection$$Lambda+0x0000019a250d0880
instanceKlass sun/rmi/transport/DGCImpl_Skel
instanceKlass  @bci sun/rmi/transport/DGCImpl$2 run ()Ljava/lang/Void; 50 <appendix> argL0 ; # sun/rmi/transport/DGCImpl$2$$Lambda+0x0000019a250cff80
instanceKlass sun/rmi/transport/DGCImpl$2
instanceKlass  @bci sun/rmi/transport/DGCImpl <clinit> ()V 89 <appendix> argL0 ; # sun/rmi/transport/DGCImpl$$Lambda+0x0000019a250cfb10
instanceKlass java/util/concurrent/RunnableScheduledFuture
instanceKlass java/util/concurrent/ScheduledFuture
instanceKlass java/util/concurrent/Delayed
instanceKlass java/util/concurrent/RunnableFuture
instanceKlass java/util/concurrent/Future
instanceKlass sun/rmi/runtime/RuntimeUtil$1
instanceKlass java/util/concurrent/ScheduledExecutorService
instanceKlass  @bci sun/rmi/runtime/RuntimeUtil <clinit> ()V 10 <appendix> argL0 ; # sun/rmi/runtime/RuntimeUtil$$Lambda+0x0000019a250cd730
instanceKlass sun/rmi/runtime/RuntimeUtil
instanceKlass sun/rmi/runtime/RuntimeUtil$GetInstanceAction
instanceKlass  @bci sun/rmi/transport/DGCImpl <clinit> ()V 43 <appendix> argL0 ; # sun/rmi/transport/DGCImpl$$Lambda+0x0000019a250cd0a0
instanceKlass  @bci sun/rmi/transport/DGCImpl <clinit> ()V 26 <appendix> argL0 ; # sun/rmi/transport/DGCImpl$$Lambda+0x0000019a250cce70
instanceKlass  @bci sun/rmi/transport/DGCImpl <clinit> ()V 6 <appendix> argL0 ; # sun/rmi/transport/DGCImpl$$Lambda+0x0000019a250ccc40
instanceKlass sun/rmi/transport/DGCImpl
instanceKlass java/rmi/dgc/DGC
instanceKlass  @bci sun/rmi/transport/ObjectTable <clinit> ()V 0 <appendix> argL0 ; # sun/rmi/transport/ObjectTable$$Lambda+0x0000019a250cc5c0
instanceKlass sun/rmi/transport/ObjectTable
instanceKlass sun/rmi/transport/Target
instanceKlass sun/rmi/registry/RegistryImpl_Skel
instanceKlass java/rmi/server/Skeleton
instanceKlass java/rmi/server/Operation
instanceKlass  @bci sun/rmi/registry/RegistryImpl <init> (I)V 96 <appendix> argL0 ; # sun/rmi/registry/RegistryImpl$$Lambda+0x0000019a250cafe8
instanceKlass java/io/ObjectInputFilter$FilterInfo
instanceKlass java/util/LinkedList$Node
instanceKlass  @bci sun/rmi/transport/tcp/TCPTransport <clinit> ()V 142 <appendix> argL0 ; # sun/rmi/transport/tcp/TCPTransport$$Lambda+0x0000019a250ca730
instanceKlass sun/rmi/transport/tcp/TCPTransport$1
instanceKlass java/util/concurrent/LinkedTransferQueue$DualNode
instanceKlass java/util/concurrent/TransferQueue
instanceKlass  @bci sun/rmi/transport/tcp/TCPTransport <clinit> ()V 59 <appendix> argL0 ; # sun/rmi/transport/tcp/TCPTransport$$Lambda+0x0000019a250c8988
instanceKlass  @bci sun/rmi/transport/tcp/TCPTransport <clinit> ()V 42 <appendix> argL0 ; # sun/rmi/transport/tcp/TCPTransport$$Lambda+0x0000019a250c8758
instanceKlass  @bci sun/rmi/transport/tcp/TCPTransport <clinit> ()V 22 <appendix> argL0 ; # sun/rmi/transport/tcp/TCPTransport$$Lambda+0x0000019a250c8528
instanceKlass  @bci sun/rmi/transport/Transport getLogLevel ()Ljava/lang/String; 0 <appendix> argL0 ; # sun/rmi/transport/Transport$$Lambda+0x0000019a250c82f8
instanceKlass sun/rmi/transport/Transport
instanceKlass  @bci sun/rmi/transport/tcp/TCPEndpoint getHostnameProperty ()Ljava/lang/String; 0 <appendix> argL0 ; # sun/rmi/transport/tcp/TCPEndpoint$$Lambda+0x0000019a250c7c18
instanceKlass sun/rmi/transport/tcp/TCPEndpoint
instanceKlass sun/rmi/transport/Endpoint
instanceKlass sun/rmi/transport/LiveRef
instanceKlass  @bci sun/rmi/registry/RegistryImpl <clinit> ()V 26 <appendix> argL0 ; # sun/rmi/registry/RegistryImpl$$Lambda+0x0000019a250c72b8
instanceKlass java/io/ObjectInputFilter
instanceKlass sun/security/provider/AbstractDrbg$NonceProvider
instanceKlass  @bci sun/security/provider/AbstractDrbg$SeederHolder <clinit> ()V 42 <appendix> member <vmtarget> ; # sun/security/provider/AbstractDrbg$SeederHolder$$Lambda+0x0000019a250c6728
instanceKlass  @cpi sun/security/provider/AbstractDrbg$SeederHolder 91 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a2508fc00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a2508f800
instanceKlass sun/nio/fs/BasicFileAttributesHolder
instanceKlass sun/nio/fs/WindowsDirectoryStream$WindowsDirectoryIterator
instanceKlass sun/nio/fs/WindowsDirectoryStream
instanceKlass java/nio/file/DirectoryStream
instanceKlass java/nio/file/Files$AcceptAllFilter
instanceKlass java/net/NetworkInterface$1
instanceKlass sun/security/provider/ByteArrayAccess$BE
instanceKlass sun/security/provider/ByteArrayAccess
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass sun/security/provider/SeedGenerator$1
instanceKlass sun/security/util/MessageDigestSpi2
instanceKlass sun/security/jca/GetInstance$Instance
instanceKlass sun/security/jca/GetInstance
instanceKlass java/security/MessageDigestSpi
instanceKlass sun/security/provider/SeedGenerator
instanceKlass sun/security/provider/AbstractDrbg$SeederHolder
instanceKlass java/security/DrbgParameters$NextBytes
instanceKlass  @bci sun/security/provider/AbstractDrbg <clinit> ()V 12 <appendix> argL0 ; # sun/security/provider/AbstractDrbg$$Lambda+0x0000019a250c2928
instanceKlass  @cpi sun/security/provider/AbstractDrbg 383 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a2508f400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a2508f000
instanceKlass sun/security/provider/EntropySource
instanceKlass sun/security/provider/AbstractDrbg
instanceKlass java/security/DrbgParameters$Instantiation
instanceKlass java/security/DrbgParameters
instanceKlass sun/security/provider/MoreDrbgParameters
instanceKlass  @bci sun/security/provider/DRBG <init> (Ljava/security/SecureRandomParameters;)V 26 <appendix> argL0 ; # sun/security/provider/DRBG$$Lambda+0x0000019a250c1328
instanceKlass java/security/SecureRandomSpi
instanceKlass jdk/internal/event/Event
instanceKlass sun/security/util/SecurityProviderConstants
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/util/SecurityConstants
instanceKlass java/util/AbstractList$Itr
instanceKlass sun/security/jca/ProviderList$2
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass java/rmi/server/UID
instanceKlass java/rmi/server/ObjID
instanceKlass sun/rmi/server/WeakClassHashMap
instanceKlass  @bci sun/rmi/server/UnicastServerRef <clinit> ()V 49 <appendix> argL0 ; # sun/rmi/server/UnicastServerRef$$Lambda+0x0000019a25078358
instanceKlass  @bci sun/rmi/server/UnicastServerRef <clinit> ()V 32 <appendix> argL0 ; # sun/rmi/server/UnicastServerRef$$Lambda+0x0000019a25078128
instanceKlass  @bci sun/rmi/server/UnicastServerRef <clinit> ()V 0 <appendix> argL0 ; # sun/rmi/server/UnicastServerRef$$Lambda+0x0000019a25077ef8
instanceKlass  @bci sun/rmi/server/UnicastRef <clinit> ()V 21 <appendix> argL0 ; # sun/rmi/server/UnicastRef$$Lambda+0x0000019a250778b8
instanceKlass  @bci sun/rmi/server/Util <clinit> ()V 32 <appendix> argL0 ; # sun/rmi/server/Util$$Lambda+0x0000019a25077688
instanceKlass java/util/logging/StreamHandler$1
instanceKlass java/util/logging/Handler$1
instanceKlass java/util/logging/ErrorManager
instanceKlass jdk/internal/logger/SimpleConsoleLogger$Formatting
instanceKlass sun/util/logging/PlatformLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration
instanceKlass  @bci java/util/logging/SimpleFormatter <init> ()V 5 <appendix> argL0 ; # java/util/logging/SimpleFormatter$$Lambda+0x0000019a25075698
instanceKlass java/util/logging/Formatter
instanceKlass sun/rmi/runtime/Log$LoggerLog$1
instanceKlass sun/rmi/runtime/Log$LoggerLogFactory
instanceKlass sun/rmi/runtime/Log$LogFactory
instanceKlass  @bci sun/rmi/runtime/Log <clinit> ()V 22 <appendix> argL0 ; # sun/rmi/runtime/Log$$Lambda+0x0000019a250741a0
instanceKlass sun/rmi/runtime/Log
instanceKlass  @bci sun/rmi/server/Util <clinit> ()V 0 <appendix> argL0 ; # sun/rmi/server/Util$$Lambda+0x0000019a250734f8
instanceKlass sun/rmi/server/Util
instanceKlass sun/rmi/server/UnicastRef
instanceKlass sun/rmi/server/Dispatcher
instanceKlass java/rmi/server/ServerRef
instanceKlass java/rmi/server/RemoteRef
instanceKlass java/io/Externalizable
instanceKlass java/rmi/server/RemoteObject
instanceKlass java/rmi/registry/Registry
instanceKlass java/rmi/registry/LocateRegistry
instanceKlass java/net/DefaultInterface
instanceKlass java/net/Inet6Address$Inet6AddressHolder
instanceKlass java/net/InterfaceAddress
instanceKlass java/net/NetworkInterface
instanceKlass java/net/InetAddress$PlatformResolver
instanceKlass java/net/spi/InetAddressResolver
instanceKlass java/net/spi/InetAddressResolver$LookupPolicy
instanceKlass java/net/Inet4AddressImpl
instanceKlass java/net/Inet6AddressImpl
instanceKlass java/net/InetAddressImpl
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Node
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Index
instanceKlass java/util/concurrent/ConcurrentNavigableMap
instanceKlass java/net/InetAddress$InetAddressHolder
instanceKlass java/net/InetAddress$1
instanceKlass jdk/internal/access/JavaNetInetAddressAccess
instanceKlass java/net/InetAddress
instanceKlass com/intellij/execution/rmi/IdeaWatchdogAware
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass com/intellij/execution/rmi/IdeaWatchdogImpl
# instanceKlass java/awt/DesktopActionsHandler$$$+0x0000019a25070d38
# instanceKlass com/jetbrains/DesktopActions$$$+0x0000019a2503da50
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a2508ec00
instanceKlass  @bci com/jetbrains/internal/Mapping$ProxyConversion wrapNonNull (Lcom/jetbrains/internal/AccessContext$Method;)V 71 <appendix> member <vmtarget> ; # com/jetbrains/internal/Mapping$ProxyConversion$$Lambda+0x0000019a25070b00
instanceKlass  @cpi com/mysql/cj/protocol/ExportControlled 611 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a2508e800
instanceKlass java/awt/Desktop$DesktopActionsHandler
instanceKlass java/awt/Desktop
instanceKlass com/jetbrains/DesktopActions
instanceKlass com/jetbrains/JBR$DesktopActions__Holder
instanceKlass  @bci java/lang/invoke/BootstrapMethodInvoker invoke (Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 111 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2508e400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2508e000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2508dc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2508d800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2508d400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2508d000
instanceKlass  @bci java/lang/invoke/BootstrapMethodInvoker invoke (Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 894 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2508cc00
# instanceKlass com/jetbrains/ServiceApi$$$+0x0000019a2503d160
instanceKlass jdk/internal/org/objectweb/asm/ClassReader
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass  @bci java/util/stream/Collectors toUnmodifiableSet ()Ljava/util/stream/Collector; 19 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x0000019a2506fa20
instanceKlass  @bci java/util/stream/Collectors toUnmodifiableSet ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x0000019a2506f7c8
instanceKlass java/util/function/BinaryOperator
instanceKlass  @bci java/util/stream/Collectors toUnmodifiableSet ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000039
instanceKlass java/util/function/BiConsumer
instanceKlass  @bci java/util/stream/Collectors toUnmodifiableSet ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000045
instanceKlass java/util/stream/Collector
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/stream/Collectors
instanceKlass  @bci com/jetbrains/internal/ProxyGenerator getSupportedExtensions ()Ljava/util/Set; 24 <appendix> argL0 ; # com/jetbrains/internal/ProxyGenerator$$Lambda+0x0000019a2506f370
instanceKlass  @bci com/jetbrains/internal/ProxyGenerator getSupportedExtensions ()Ljava/util/Set; 14 <appendix> argL0 ; # com/jetbrains/internal/ProxyGenerator$$Lambda+0x0000019a2506f110
instanceKlass  @cpi java/util/ResourceBundle$ResourceBundleControlProviderHolder 125 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a2508c800
instanceKlass java/util/HashMap$HashMapSpliterator
instanceKlass jdk/internal/vm/annotation/IntrinsicCandidate
instanceKlass sun/reflect/generics/tree/BooleanSignature
instanceKlass sun/reflect/generics/reflectiveObjects/GenericArrayTypeImpl
instanceKlass java/lang/reflect/WildcardType
instanceKlass sun/reflect/generics/tree/ArrayTypeSignature
instanceKlass  @bci com/jetbrains/internal/AccessContext canAccess (Ljava/lang/Class;)Z 6 <appendix> member <vmtarget> ; # com/jetbrains/internal/AccessContext$$Lambda+0x0000019a2506d4f8
instanceKlass com/jetbrains/internal/AccessContext$Method
instanceKlass java/lang/Deprecated
instanceKlass  @bci com/jetbrains/internal/ProxyGenerator generateMethod (Ljava/lang/reflect/Method;Ljava/lang/invoke/MethodHandle;Lcom/jetbrains/internal/Mapping$Method;Ljava/lang/Enum;Z)V 26 <appendix> member <vmtarget> ; # com/jetbrains/internal/ProxyGenerator$$Lambda+0x0000019a2506ce68
instanceKlass  @bci com/jetbrains/internal/JBRApi$DynamicCallTargetKey equals (Ljava/lang/Object;)Z 2 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2508c400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2508c000
instanceKlass  @bci com/jetbrains/internal/JBRApi$DynamicCallTargetKey equals (Ljava/lang/Object;)Z 2 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2508bc00
instanceKlass  @bci com/jetbrains/internal/JBRApi$DynamicCallTargetKey equals (Ljava/lang/Object;)Z 2 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2508b800
instanceKlass  @bci com/jetbrains/internal/JBRApi$DynamicCallTargetKey equals (Ljava/lang/Object;)Z 2 <appendix> form names 9 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2508b400
instanceKlass  @bci com/jetbrains/internal/JBRApi$DynamicCallTargetKey equals (Ljava/lang/Object;)Z 2 <appendix> form names 9 function resolvedHandle form names 5 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a2508b000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a2508ac00
instanceKlass java/lang/invoke/MethodHandleImpl$LoopClauses
instanceKlass java/lang/invoke/MethodHandleImpl$CasesHolder
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2508a800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2508a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a2508a000
instanceKlass  @bci com/jetbrains/internal/JBRApi$DynamicCallTargetKey equals (Ljava/lang/Object;)Z 2 <appendix> form names 7 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a25089c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25089800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25089400
instanceKlass com/jetbrains/internal/Mapping$Query
instanceKlass com/jetbrains/internal/Mapping
instanceKlass sun/reflect/generics/tree/Wildcard
instanceKlass sun/reflect/generics/tree/BottomSignature
instanceKlass sun/reflect/generics/reflectiveObjects/ParameterizedTypeImpl
instanceKlass sun/reflect/generics/reflectiveObjects/LazyReflectiveObjectGenerator
instanceKlass sun/reflect/generics/tree/MethodTypeSignature
instanceKlass sun/reflect/generics/tree/TypeVariableSignature
instanceKlass com/jetbrains/Extension
instanceKlass sun/reflect/generics/tree/ClassSignature
instanceKlass sun/reflect/generics/tree/Signature
instanceKlass sun/reflect/generics/tree/FormalTypeParameter
instanceKlass java/lang/reflect/TypeVariable
instanceKlass sun/reflect/generics/repository/AbstractRepository
instanceKlass java/lang/reflect/ParameterizedType
instanceKlass com/jetbrains/internal/ASMUtils
instanceKlass com/jetbrains/internal/AccessContext
instanceKlass com/jetbrains/internal/Mapping$Context
instanceKlass com/jetbrains/exported/JBRApiSupport$Proxy
instanceKlass com/jetbrains/internal/ProxyGenerator
instanceKlass java/lang/Class$AnnotationData
instanceKlass java/lang/annotation/Documented
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25089000
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/Method;Ljava/lang/Class;)V 23 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x0000019a25066088
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/ProxyGenerator$ProxyMethod;)V 10 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x0000019a25065e38
instanceKlass java/util/StringJoiner
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass  @bci java/lang/reflect/Proxy getLoader (Ljava/lang/Module;)Ljava/lang/ClassLoader; 6 <appendix> member <vmtarget> ; # java/lang/reflect/Proxy$$Lambda+0x0000019a250656b0
instanceKlass  @bci java/lang/module/ModuleDescriptor$Builder packages (Ljava/util/Set;)Ljava/lang/module/ModuleDescriptor$Builder; 17 <appendix> argL0 ; # java/lang/module/ModuleDescriptor$Builder$$Lambda+0x800000002
instanceKlass jdk/internal/module/Checks
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass  @bci java/lang/reflect/Proxy$ProxyBuilder getDynamicModule (Ljava/lang/ClassLoader;)Ljava/lang/Module; 4 <appendix> argL0 ; # java/lang/reflect/Proxy$ProxyBuilder$$Lambda+0x0000019a25065250
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass java/lang/PublicMethods
instanceKlass java/util/Collections$1
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass  @bci java/lang/reflect/Proxy getProxyConstructor (Ljava/lang/Class;Ljava/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor; 35 <appendix> argL0 ; # java/lang/reflect/Proxy$$Lambda+0x0000019a25063f20
instanceKlass java/lang/reflect/Proxy
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass java/lang/annotation/Target
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass java/util/OptionalInt
instanceKlass java/util/EnumMap$EnumMapIterator
instanceKlass java/util/stream/ReduceOps$6ReducingSink
instanceKlass java/util/stream/Sink$OfInt
instanceKlass java/util/function/IntConsumer
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/ReduceOps
instanceKlass  @bci java/util/stream/IntPipeline max ()Ljava/util/OptionalInt; 1 <appendix> argL0 ; # java/util/stream/IntPipeline$$Lambda+0x0000019a2505d680
instanceKlass java/util/function/IntBinaryOperator
instanceKlass java/util/stream/IntStream
instanceKlass  @bci com/jetbrains/internal/JBRApi init (Ljava/io/InputStream;Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/Class;Ljava/util/Map;Ljava/util/function/Function;)V 58 <appendix> argL0 ; # com/jetbrains/internal/JBRApi$$Lambda+0x0000019a2505c100
instanceKlass java/util/function/ToIntFunction
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass  @bci com/jetbrains/internal/JBRApi$DynamicCallTargetKey hashCode ()I 1 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25088c00
instanceKlass  @bci com/jetbrains/internal/JBRApi$DynamicCallTargetKey hashCode ()I 1 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25088800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25088400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25088000
instanceKlass  @bci com/jetbrains/internal/JBRApi$DynamicCallTargetKey hashCode ()I 1 <appendix> form names 7 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a25087c00
instanceKlass  @bci com/jetbrains/internal/JBRApi$DynamicCallTargetKey hashCode ()I 1 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25087800
instanceKlass  @bci com/jetbrains/internal/JBRApi$DynamicCallTargetKey hashCode ()I 1 <appendix> argL2 argL2 argL2 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25087400
instanceKlass  @bci java/lang/runtime/ObjectMethods bootstrap (Ljava/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;Ljava/lang/invoke/TypeDescriptor;Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/invoke/MethodHandle;)Ljava/lang/Object; 37 <appendix> argL0 ; # java/lang/runtime/ObjectMethods$$Lambda+0x0000019a2505baa0
instanceKlass java/lang/invoke/DirectMethodHandle$1
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25087000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25086c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25086800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25086400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25086000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25085c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25085800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25085400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25085000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25084c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25084800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25084400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25084000
instanceKlass java/lang/runtime/ObjectMethods$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25083c00
instanceKlass sun/invoke/util/ValueConversions$WrapperCache
instanceKlass java/lang/invoke/MethodHandles$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25083800
instanceKlass java/lang/invoke/LambdaFormEditor$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25083400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25083000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25082c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25082800
instanceKlass  @cpi com/mysql/cj/protocol/ExportControlled 593 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a25082400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25082000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25081c00
instanceKlass  @cpi com/intellij/database/remote/jdbc/helpers/JdbcNativeUtil 395 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a25081800
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccessor
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25081400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25081000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25080c00
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/MethodHandleImpl$Makers$2
instanceKlass java/lang/invoke/MethodHandleImpl$Makers$1
instanceKlass java/lang/invoke/MethodHandleImpl$Makers
instanceKlass  @bci java/lang/invoke/BootstrapMethodInvoker invoke (Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 862 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25080800
instanceKlass  @bci com/jetbrains/internal/JBRApi$DynamicCallTargetKey hashCode ()I 1 <appendix> argL1 argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25080400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000019a25080000
instanceKlass java/lang/runtime/ObjectMethods
instanceKlass  @bci com/jetbrains/internal/ProxyRepository$Registry readEntries (Ljava/io/InputStream;)Ljava/lang/String; 568 <appendix> member <vmtarget> ; # com/jetbrains/internal/ProxyRepository$Registry$$Lambda+0x0000019a2505a200
instanceKlass  @bci com/jetbrains/internal/ProxyRepository$Registry readEntries (Ljava/io/InputStream;)Ljava/lang/String; 189 <appendix> member <vmtarget> ; # com/jetbrains/internal/ProxyRepository$Registry$$Lambda+0x0000019a25059fa8
instanceKlass com/jetbrains/internal/Proxy$Info
instanceKlass  @bci com/jetbrains/internal/ProxyRepository$Registry readEntries (Ljava/io/InputStream;)Ljava/lang/String; 165 <appendix> member <vmtarget> ; # com/jetbrains/internal/ProxyRepository$Registry$$Lambda+0x0000019a25059b20
instanceKlass com/jetbrains/internal/ProxyRepository$Registry$Entry
instanceKlass  @bci jdk/internal/module/SystemModuleFinders$SystemModuleReader open (Ljava/lang/String;)Ljava/util/Optional; 6 <appendix> member <vmtarget> ; # jdk/internal/module/SystemModuleFinders$SystemModuleReader$$Lambda+0x0000019a250593c8
instanceKlass com/jetbrains/internal/ProxyRepository$Registry
instanceKlass com/jetbrains/internal/Proxy
instanceKlass com/jetbrains/internal/ProxyRepository
instanceKlass  @bci com/jetbrains/internal/Utils <clinit> ()V 8 <appendix> argL0 ; # com/jetbrains/internal/Utils$$Lambda+0x0000019a25058ac0
instanceKlass  @bci com/jetbrains/internal/Utils <clinit> ()V 0 <appendix> argL0 ; # com/jetbrains/internal/Utils$$Lambda+0x0000019a25058870
instanceKlass com/jetbrains/internal/Utils
instanceKlass com/jetbrains/internal/JBRApi
instanceKlass  @bci com/jetbrains/JBR$Metadata <clinit> ()V 226 <appendix> argL0 ; # com/jetbrains/JBR$Metadata$$Lambda+0x0000019a2503c638
instanceKlass com/jetbrains/JBR$Metadata
instanceKlass com/jetbrains/Provides
instanceKlass com/jetbrains/Provided
instanceKlass com/jetbrains/Service
instanceKlass com/jetbrains/JBR$ServiceApi
instanceKlass com/jetbrains/exported/JBRApiSupport
instanceKlass com/jetbrains/JBR
instanceKlass com/jetbrains/DesktopActions$Handler
instanceKlass com/intellij/database/remote/toolkit/JbrApiHandler
instanceKlass com/intellij/openapi/util/Setter
instanceKlass com/intellij/database/remote/toolkit/RemoteDesktopActionsHandler
instanceKlass com/intellij/execution/rmi/RemoteObject$ExceptionProcessor
instanceKlass  @bci sun/security/ec/ParametersMap get (Ljava/util/function/Function;Ljava/security/spec/AlgorithmParameterSpec;)Ljava/lang/Object; 29 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a2503b400
instanceKlass java/sql/DriverInfo
instanceKlass java/net/URLClassLoader$2
instanceKlass java/sql/DriverManager
instanceKlass  @bci java/util/concurrent/Executors$AutoShutdownDelegatedExecutorService <init> (Ljava/util/concurrent/ExecutorService;)V 6 <appendix> member <vmtarget> ; # java/util/concurrent/Executors$AutoShutdownDelegatedExecutorService$$Lambda+0x0000019a250578c8
instanceKlass  @cpi sun/nio/ch/NioSocketImpl 908 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000019a2503b000
instanceKlass jdk/internal/vm/ThreadContainers
instanceKlass jdk/internal/vm/StackableScope
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass java/util/concurrent/Executors$DelegatedExecutorService
instanceKlass java/util/concurrent/ExecutorService
instanceKlass java/util/concurrent/Executor
instanceKlass java/util/concurrent/Executors
instanceKlass  @bci com/mysql/cj/jdbc/AbandonedConnectionCleanupThread <clinit> ()V 51 <appendix> argL0 ; # com/mysql/cj/jdbc/AbandonedConnectionCleanupThread$$Lambda+0x0000019a2503a448
instanceKlass com/mysql/cj/jdbc/AbandonedConnectionCleanupThread
instanceKlass com/mysql/cj/jdbc/JdbcPropertySet
instanceKlass com/mysql/cj/conf/PropertySet
instanceKlass java/sql/Connection
instanceKlass java/sql/Wrapper
instanceKlass com/mysql/cj/jdbc/NonRegisteringDriver
instanceKlass java/sql/Driver
instanceKlass  @bci java/io/FilePermissionCollection add (Ljava/security/Permission;)V 68 <appendix> argL0 ; # java/io/FilePermissionCollection$$Lambda+0x0000019a25055758
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/access/JavaIOFilePermissionAccess
instanceKlass sun/net/www/MessageHeader
instanceKlass java/net/URLConnection
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/net/URLClassLoader$1
instanceKlass com/intellij/database/remote/jdbc/impl/ReflectionHelper
instanceKlass com/intellij/openapi/util/io/FileUtilRt$RepeatableIOOperation
instanceKlass java/nio/file/DirectoryStream$Filter
instanceKlass java/nio/file/FileVisitor
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass com/intellij/openapi/util/io/FileUtilRt
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass jdk/internal/util/ByteArray
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
instanceKlass java/util/TimeZone
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass jdk/internal/jimage/decompressor/ZipDecompressor
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressor
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressorFactory
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressorRepository
instanceKlass jdk/internal/jimage/decompressor/CompressedResourceHeader
instanceKlass  @bci jdk/internal/jimage/BasicImageReader getResourceBuffer (Ljdk/internal/jimage/ImageLocation;)Ljava/nio/ByteBuffer; 168 <appendix> member <vmtarget> ; # jdk/internal/jimage/BasicImageReader$$Lambda+0x0000019a250501a8
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressor$StringsProvider
instanceKlass java/util/TimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass java/util/AbstractMap$SimpleEntry
instanceKlass jdk/internal/jimage/ImageBufferCache$2
instanceKlass jdk/internal/jimage/ImageBufferCache
instanceKlass jdk/internal/module/ModulePatcher$PatchedModuleReader
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcNativeUtil
instanceKlass com/intellij/database/remote/jdbc/impl/JdbcClassLoader
instanceKlass org/bouncycastle/internal/asn1/isara/IsaraObjectIdentifiers
instanceKlass org/bouncycastle/pqc/asn1/PQCObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/drbg/EntropyDaemon
instanceKlass org/bouncycastle/crypto/prng/EntropySourceProvider
instanceKlass org/bouncycastle/crypto/prng/EntropySource
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass org/bouncycastle/crypto/Digest
instanceKlass org/bouncycastle/jcajce/provider/drbg/DRBG
instanceKlass org/bouncycastle/asn1/x509/X509ObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/asymmetric/RSA
instanceKlass org/bouncycastle/internal/asn1/eac/EACObjectIdentifiers
instanceKlass org/bouncycastle/internal/asn1/cms/CMSObjectIdentifiers
instanceKlass org/bouncycastle/util/Properties$2
instanceKlass org/bouncycastle/util/Properties$1
instanceKlass org/bouncycastle/util/Properties
instanceKlass org/bouncycastle/internal/asn1/bsi/BSIObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/asymmetric/EC
instanceKlass org/bouncycastle/jcajce/provider/asymmetric/DH
instanceKlass org/bouncycastle/jcajce/provider/asymmetric/dsa/DSAUtil
instanceKlass org/bouncycastle/crypto/params/AsymmetricKeyParameter
instanceKlass org/bouncycastle/jce/spec/ECNamedCurveGenParameterSpec
instanceKlass java/security/spec/RSAKeyGenParameterSpec
instanceKlass org/bouncycastle/jcajce/provider/asymmetric/compositesignatures/CompositeIndex
instanceKlass org/bouncycastle/jcajce/provider/asymmetric/EXTERNAL
instanceKlass org/bouncycastle/jcajce/provider/asymmetric/EXTERNAL$ExternalKeyInfoConverter
instanceKlass org/bouncycastle/jcajce/provider/asymmetric/COMPOSITE
instanceKlass org/bouncycastle/jcajce/util/ProviderJcaJceHelper
instanceKlass org/bouncycastle/asn1/sec/SECObjectIdentifiers
instanceKlass org/bouncycastle/asn1/ASN1Choice
instanceKlass org/bouncycastle/internal/asn1/edec/EdECObjectIdentifiers
instanceKlass java/security/PublicKey
instanceKlass java/security/PrivateKey
instanceKlass javax/security/auth/Destroyable
instanceKlass java/security/Key
instanceKlass java/security/spec/KeySpec
instanceKlass java/security/KeyFactorySpi
instanceKlass org/bouncycastle/jcajce/util/JcaJceHelper
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Zuc
instanceKlass org/bouncycastle/jcajce/provider/symmetric/GOST3412_2015
instanceKlass org/bouncycastle/jcajce/provider/symmetric/DSTU7624
instanceKlass org/bouncycastle/jcajce/provider/symmetric/OpenSSLPBKDF
instanceKlass org/bouncycastle/jcajce/provider/symmetric/XSalsa20
instanceKlass org/bouncycastle/jcajce/provider/symmetric/XTEA
instanceKlass org/bouncycastle/jcajce/provider/symmetric/VMPCKSA3
instanceKlass org/bouncycastle/jcajce/provider/symmetric/VMPC
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Threefish
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Twofish
instanceKlass org/bouncycastle/jcajce/provider/symmetric/TEA
instanceKlass org/bouncycastle/jcajce/provider/symmetric/SM4
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Skipjack
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Shacal2
instanceKlass org/bouncycastle/internal/asn1/gnu/GNUObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Serpent
instanceKlass org/bouncycastle/internal/asn1/kisa/KISAObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/symmetric/SEED
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Salsa20
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Rijndael
instanceKlass org/bouncycastle/jcajce/provider/symmetric/RC6
instanceKlass org/bouncycastle/jcajce/provider/symmetric/RC5
instanceKlass org/bouncycastle/jcajce/provider/symmetric/RC2
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Noekeon
instanceKlass org/bouncycastle/jcajce/provider/symmetric/IDEA
instanceKlass org/bouncycastle/jcajce/provider/symmetric/HC256
instanceKlass org/bouncycastle/jcajce/provider/symmetric/HC128
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Grain128
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Grainv1
instanceKlass org/bouncycastle/jcajce/provider/symmetric/GOST28147
instanceKlass org/bouncycastle/jcajce/provider/symmetric/DESede
instanceKlass org/bouncycastle/jcajce/provider/symmetric/DES
instanceKlass org/bouncycastle/jcajce/provider/symmetric/ChaCha
instanceKlass org/bouncycastle/jcajce/provider/symmetric/CAST6
instanceKlass org/bouncycastle/jcajce/provider/symmetric/CAST5
instanceKlass org/bouncycastle/internal/asn1/ntt/NTTObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Camellia
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Blowfish
instanceKlass org/bouncycastle/internal/asn1/nsri/NSRIObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/symmetric/ARIA
instanceKlass org/bouncycastle/jcajce/provider/symmetric/ARC4
instanceKlass org/bouncycastle/asn1/bc/BCObjectIdentifiers
instanceKlass java/security/Provider$UString
instanceKlass org/bouncycastle/jcajce/provider/symmetric/AES
instanceKlass org/bouncycastle/crypto/params/DHValidationParameters
instanceKlass org/bouncycastle/crypto/params/DHParameters
instanceKlass org/bouncycastle/asn1/x9/X9ObjectIdentifiers
instanceKlass org/bouncycastle/crypto/CryptoServicesRegistrar$Property
instanceKlass org/bouncycastle/util/encoders/HexEncoder
instanceKlass org/bouncycastle/util/encoders/Encoder
instanceKlass org/bouncycastle/util/encoders/Hex
instanceKlass org/bouncycastle/crypto/params/DSAValidationParameters
instanceKlass org/bouncycastle/crypto/params/DSAParameters
instanceKlass org/bouncycastle/crypto/CipherParameters
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass java/lang/invoke/VarHandles
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass org/bouncycastle/crypto/CryptoServicesRegistrar$1
instanceKlass org/bouncycastle/crypto/CryptoServicesRegistrar$ThreadLocalSecureRandomProvider
instanceKlass org/bouncycastle/crypto/CryptoServicesConstraints
instanceKlass org/bouncycastle/crypto/SecureRandomProvider
instanceKlass org/bouncycastle/crypto/CryptoServicesRegistrar
instanceKlass org/bouncycastle/jcajce/provider/symmetric/Poly1305
instanceKlass org/bouncycastle/jcajce/provider/symmetric/SipHash128
instanceKlass org/bouncycastle/jcajce/provider/symmetric/SipHash
instanceKlass org/bouncycastle/jcajce/provider/symmetric/SCRYPT
instanceKlass org/bouncycastle/jcajce/provider/symmetric/TLSKDF
instanceKlass org/bouncycastle/jcajce/provider/symmetric/PBEPKCS12
instanceKlass org/bouncycastle/jcajce/provider/symmetric/PBEPBKDF2
instanceKlass org/bouncycastle/jcajce/provider/symmetric/PBEPBKDF1
instanceKlass org/bouncycastle/jcajce/provider/digest/Blake3
instanceKlass org/bouncycastle/jcajce/provider/digest/Haraka
instanceKlass org/bouncycastle/asn1/ua/UAObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/digest/DSTU7564
instanceKlass org/bouncycastle/jcajce/provider/digest/Blake2s
instanceKlass org/bouncycastle/internal/asn1/misc/MiscObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/digest/Blake2b
instanceKlass org/bouncycastle/internal/asn1/iso/ISOIECObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/digest/Whirlpool
instanceKlass org/bouncycastle/jcajce/provider/digest/Tiger
instanceKlass org/bouncycastle/asn1/gm/GMObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/digest/SM3
instanceKlass org/bouncycastle/jcajce/provider/digest/Skein
instanceKlass org/bouncycastle/jcajce/provider/digest/SHA3
instanceKlass org/bouncycastle/jcajce/provider/digest/SHA512
instanceKlass org/bouncycastle/jcajce/provider/digest/SHA384
instanceKlass org/bouncycastle/jcajce/provider/digest/SHA256
instanceKlass org/bouncycastle/asn1/nist/NISTObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/digest/SHA224
instanceKlass org/bouncycastle/jcajce/provider/digest/RIPEMD320
instanceKlass org/bouncycastle/jcajce/provider/digest/RIPEMD256
instanceKlass org/bouncycastle/jcajce/provider/digest/RIPEMD160
instanceKlass org/bouncycastle/asn1/teletrust/TeleTrusTObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/digest/RIPEMD128
instanceKlass org/bouncycastle/internal/asn1/oiw/OIWObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/digest/SHA1
instanceKlass org/bouncycastle/internal/asn1/iana/IANAObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/digest/MD5
instanceKlass org/bouncycastle/jcajce/provider/digest/MD4
instanceKlass org/bouncycastle/asn1/ASN1ObjectIdentifier$OidHandle
instanceKlass org/bouncycastle/asn1/pkcs/PKCSObjectIdentifiers
instanceKlass org/bouncycastle/jcajce/provider/digest/MD2
instanceKlass org/bouncycastle/jcajce/provider/digest/Keccak
instanceKlass org/bouncycastle/internal/asn1/rosstandart/RosstandartObjectIdentifiers
instanceKlass org/bouncycastle/util/Arrays
instanceKlass org/bouncycastle/asn1/OIDTokenizer
instanceKlass org/bouncycastle/asn1/ASN1Tag
instanceKlass org/bouncycastle/asn1/ASN1Type
instanceKlass org/bouncycastle/asn1/ASN1Object
instanceKlass org/bouncycastle/util/Encodable
instanceKlass org/bouncycastle/asn1/ASN1Encodable
instanceKlass org/bouncycastle/asn1/cryptopro/CryptoProObjectIdentifiers
instanceKlass java/security/Provider$Service
instanceKlass org/bouncycastle/jcajce/provider/digest/GOST3411
instanceKlass org/bouncycastle/jcajce/provider/util/AlgorithmProvider
instanceKlass org/bouncycastle/jce/provider/BouncyCastleProvider$1
instanceKlass jdk/internal/math/MathUtils
instanceKlass jdk/internal/math/DoubleToDecimal
instanceKlass org/bouncycastle/jce/provider/BouncyCastleProvider$JcaCryptoService
instanceKlass java/security/cert/PKIXCertPathChecker
instanceKlass java/security/cert/CertPathChecker
instanceKlass org/bouncycastle/jcajce/provider/symmetric/util/ClassUtil
instanceKlass org/bouncycastle/util/Strings$1
instanceKlass org/bouncycastle/util/StringList
instanceKlass org/bouncycastle/util/Iterable
instanceKlass org/bouncycastle/util/Strings
instanceKlass java/util/StringTokenizer
instanceKlass javax/crypto/spec/DHParameterSpec
instanceKlass java/security/spec/AlgorithmParameterSpec
instanceKlass org/bouncycastle/jce/provider/BouncyCastleProviderConfiguration
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass java/util/Collections$EmptyIterator
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass java/lang/System$Logger
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 43 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x800000048
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 38 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004a
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 16 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x800000049
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 11 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004b
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/function/Consumer
instanceKlass java/util/stream/FindOps
instanceKlass  @bci java/util/logging/Level$KnownLevel findByName (Ljava/lang/String;Ljava/util/function/Function;)Ljava/util/Optional; 29 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000023
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000019a25004400
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory$LazyStaticHolder
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass java/lang/Class$3
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass java/util/Spliterator
instanceKlass  @bci java/util/logging/Level findLevel (Ljava/lang/String;)Ljava/util/logging/Level; 13 <appendix> argL0 ; # java/util/logging/Level$$Lambda+0x800000011
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass java/util/Collections$3
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass java/util/function/Predicate
instanceKlass  @bci java/rmi/server/ObjID toString ()Ljava/lang/String; 25 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25004000
instanceKlass java/net/URI$Parser
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/nio/file/Paths
instanceKlass java/util/logging/LogManager$2
instanceKlass java/lang/System$LoggerFinder
instanceKlass java/security/Security$1
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/io/FileInputStream$1
instanceKlass java/util/Properties$LineReader
instanceKlass  @bci java/security/Security <clinit> ()V 9 <appendix> argL0 ; # java/security/Security$$Lambda+0x80000000b
instanceKlass java/security/Security
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/LogManager
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 49 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000022
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 19 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000021
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass javax/security/auth/login/Configuration$Parameters
instanceKlass java/security/Policy$Parameters
instanceKlass java/security/cert/CertStoreParameters
instanceKlass java/security/SecureRandomParameters
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass org/bouncycastle/jcajce/provider/util/AsymmetricKeyInfoConverter
instanceKlass org/bouncycastle/crypto/CryptoServiceProperties
instanceKlass org/bouncycastle/jcajce/provider/config/ProviderConfiguration
instanceKlass org/bouncycastle/jcajce/provider/config/ConfigurableProvider
instanceKlass com/intellij/execution/rmi/ssl/SslEntityReader$Entity
instanceKlass java/io/Reader
instanceKlass  @bci java/util/regex/CharPredicates ASCII_DIGIT ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000025
instanceKlass java/util/regex/CharPredicates
instanceKlass  @bci java/util/regex/Pattern Single (I)Ljava/util/regex/Pattern$BmpCharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000029
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass  @bci java/util/regex/Pattern DOT ()Ljava/util/regex/Pattern$CharPredicate; 0 <appendix> argL0 ; # java/util/regex/Pattern$$Lambda+0x0000019a25045d10
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass com/intellij/execution/rmi/RemoteObject
instanceKlass java/rmi/server/Unreferenced
instanceKlass com/intellij/database/remote/jdbc/RemoteDriver
instanceKlass com/intellij/execution/rmi/RemoteCastable
instanceKlass com/intellij/execution/rmi/ssl/SslEntityReader
instanceKlass java/rmi/server/RMISocketFactory
instanceKlass java/rmi/server/RMIClientSocketFactory
instanceKlass java/rmi/server/RMIServerSocketFactory
instanceKlass com/intellij/execution/rmi/IdeaWatchdog
instanceKlass java/rmi/Remote
instanceKlass jdk/internal/misc/PreviewFeatures
instanceKlass jdk/internal/misc/MainMethodFinder
instanceKlass com/intellij/execution/rmi/RemoteServer
instanceKlass sun/security/util/Debug
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass jdk/internal/loader/Resource
instanceKlass java/util/zip/ZipEntry
instanceKlass java/util/zip/ZipFile$2
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/TreeMap$Entry
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass java/util/SequencedMap
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass java/util/SequencedSet
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/util/zip/ZipFile$Source$RandomAccessFileAccessor
instanceKlass java/util/zip/ZipFile$Source$FileAccessor
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass jdk/internal/loader/NativeLibraries$3
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext$1
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext
instanceKlass jdk/internal/loader/NativeLibraries$2
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass java/nio/charset/CoderResult
instanceKlass java/lang/Readable
instanceKlass sun/nio/cs/GBK$EncodeHolder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass java/lang/ThreadLocal
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/net/util/URLUtil
instanceKlass sun/launcher/LauncherHelper
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass  @bci java/lang/WeakPairMap computeIfAbsent (Ljava/lang/Object;Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object; 18 <appendix> member <vmtarget> ; # java/lang/WeakPairMap$$Lambda+0x0000019a25042480
instanceKlass  @bci java/lang/Module implAddExportsOrOpens (Ljava/lang/String;Ljava/lang/Module;ZZ)V 145 <appendix> argL0 ; # java/lang/Module$$Lambda+0x0000019a25041ae8
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass java/util/concurrent/ForkJoinPool$ManagedBlocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 6 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000019a25000400
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/util/ArrayList$Itr
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass  @bci jdk/internal/module/ModuleBootstrap decode (Ljava/lang/String;Ljava/lang/String;Z)Ljava/util/Map; 193 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x0000019a25041690
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass sun/security/action/GetBooleanAction
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$1
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/reflect/Array
instanceKlass java/lang/invoke/Invokers
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/Void
instanceKlass jdk/internal/util/StrongReferenceKey
instanceKlass jdk/internal/util/ReferenceKey
instanceKlass jdk/internal/util/ReferencedKeyMap
instanceKlass java/lang/invoke/MethodType$1
instanceKlass java/lang/invoke/LambdaMetafactory
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/ModuleLayer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/util/AbstractMap$1$1
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/lang/module/Configuration
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass sun/net/util/IPAddressUtil$MASKS
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass java/lang/Module$EnableNativeAccess
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/lang/reflect/AccessFlag$18
instanceKlass java/lang/reflect/AccessFlag$17
instanceKlass java/lang/reflect/AccessFlag$16
instanceKlass java/lang/reflect/AccessFlag$15
instanceKlass java/lang/reflect/AccessFlag$14
instanceKlass java/lang/reflect/AccessFlag$13
instanceKlass java/lang/reflect/AccessFlag$12
instanceKlass java/lang/reflect/AccessFlag$11
instanceKlass java/lang/reflect/AccessFlag$10
instanceKlass java/lang/reflect/AccessFlag$9
instanceKlass java/lang/reflect/AccessFlag$8
instanceKlass java/lang/reflect/AccessFlag$7
instanceKlass java/lang/reflect/AccessFlag$6
instanceKlass java/lang/reflect/AccessFlag$5
instanceKlass java/lang/reflect/AccessFlag$4
instanceKlass java/lang/reflect/AccessFlag$3
instanceKlass java/lang/reflect/AccessFlag$2
instanceKlass java/lang/reflect/AccessFlag$1
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$default
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/StrictMath
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/Character$CharacterCache
instanceKlass java/util/HexFormat
instanceKlass jdk/internal/util/ClassFileDumper
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass jdk/internal/misc/Blocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass java/util/Collections
instanceKlass java/lang/Thread$ThreadIdentifiers
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass java/lang/Integer$IntegerCache
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$2
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass java/io/Writer
instanceKlass java/io/PrintStream$1
instanceKlass jdk/internal/access/JavaIOPrintStreamAccess
instanceKlass jdk/internal/misc/InternalLock
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/CharacterData
instanceKlass java/lang/Runtime
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass java/util/HashMap$Node
instanceKlass java/util/Map$Entry
instanceKlass java/lang/StringCoding
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/DoubleByte
instanceKlass sun/nio/cs/GBK$DecodeHolder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass sun/nio/cs/DelegatableDecoder
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$1
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass java/util/Arrays
instanceKlass jdk/internal/util/Preconditions$3
instanceKlass jdk/internal/util/Preconditions$2
instanceKlass jdk/internal/util/Preconditions$4
instanceKlass java/util/function/BiFunction
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/util/function/Function
instanceKlass jdk/internal/util/Preconditions
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/NativeReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/Math
instanceKlass java/lang/StringLatin1
instanceKlass jdk/internal/reflect/Reflection
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass jdk/internal/misc/VM
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/FillerObject
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/SequencedCollection
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/lang/Enum
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/foreign/abi/ABIDescriptor
instanceKlass jdk/internal/foreign/abi/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass jdk/internal/vm/StackChunk
instanceKlass jdk/internal/vm/Continuation
instanceKlass jdk/internal/vm/ContinuationScope
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread$Constants
instanceKlass java/lang/Thread$FieldHolder
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 124 7 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 3 8 1 7 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 3 1 1
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
ciInstanceKlass java/lang/System 1 1 854 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 100 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 7 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 9 12 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 9 12 1 8 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 9 12 1 1 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 12 1 1 11 7 12 1 1 10 12 10 7 12 1 1 1 9 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 8 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 7 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 8 1 10 100 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/BufferedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; java/io/PrintStream
staticfield java/lang/System err Ljava/io/PrintStream; java/io/PrintStream
ciInstanceKlass java/lang/SecurityManager 0 0 576 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 1 10 100 1 10 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 11 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 18 12 1 18 10 100 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 7 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 100 12 1 1 10 100 1 9 7 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 7 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 7 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 7 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 15 10 100 12 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/security/AccessController 1 1 295 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 100 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 7 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
ciInstanceKlass java/security/ProtectionDomain 1 1 348 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 7 1 8 1 10 12 1 10 11 10 7 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 100 1 18 12 1 1 10 7 12 1 1 1 10 7 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 7 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 398 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 7 1 10 12 10 100 12 1 1 1 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 7 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 10 12 10 12 1 1 11 7 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 152 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Comparable 1 0 12 100 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/constant/Constable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/util/Map 1 1 263 11 7 12 1 1 1 11 12 1 1 10 100 12 1 1 11 12 1 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 11 12 1 10 12 1 1 11 12 1 11 100 12 1 9 7 12 1 1 1 7 1 10 12 7 1 7 1 10 12 1 7 1 10 7 1 11 12 1 11 12 1 1 11 12 1 1 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Class 1 1 1698 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 8 1 8 1 8 1 10 7 12 1 1 1 11 12 1 1 8 1 10 12 1 10 11 100 12 1 1 1 11 7 12 1 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 7 1 7 1 10 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 100 1 10 10 12 1 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 9 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 1 10 10 10 12 1 1 10 12 1 1 10 12 10 10 12 1 1 7 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 7 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 11 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 7 1 10 9 12 1 1 10 12 7 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 100 1 10 10 12 1 1 7 1 10 12 1 1 100 11 7 1 9 12 1 1 9 12 1 7 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 10 10 12 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 7 1 9 12 1 8 1 10 12 1 7 1 10 12 1 10 12 1 1 100 1 7 1 9 12 1 100 1 8 1 10 10 7 12 1 1 1 10 12 11 7 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 11 12 7 1 11 7 12 1 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 100 1 11 12 1 10 100 12 1 1 1 10 12 1 11 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 100 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 100 1 10 12 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 15 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/reflect/AnnotatedElement 1 1 164 11 7 12 1 1 1 11 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 12 1 1 11 7 12 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 18 12 1 18 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 16 1 16 1 15 11 12 16 16 1 15 10 100 12 1 1 1 16 1 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor 1 0 17 100 1 100 1 1 1 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass java/lang/reflect/GenericDeclaration 1 0 30 7 1 7 1 7 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1
ciInstanceKlass java/lang/reflect/Type 1 1 17 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfField 1 0 21 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringBuilder 1 1 422 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 605 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 7 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 100 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 10 12 10 12 1 10 10 10 12 1 10 5 0 10 10 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 12 100 1 10 100 1 10 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 7 1 1 16 1 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciInstanceKlass java/lang/Appendable 1 0 14 100 1 100 1 1 1 1 100 1 1 1 1 1
ciInstanceKlass java/lang/CharSequence 1 1 131 11 7 12 1 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 100 12 1 1 1 11 12 1 1 11 7 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 11 12 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/AutoCloseable 1 0 12 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/io/Closeable 1 0 14 100 1 100 1 100 1 1 1 1 100 1 1 1
instanceKlass javax/security/auth/callback/UnsupportedCallbackException
instanceKlass sun/security/ec/ECOperations$IntermediateValueException
instanceKlass sun/nio/fs/WindowsException
instanceKlass java/security/PrivilegedActionException
instanceKlass sun/security/pkcs11/wrapper/PKCS11Exception
instanceKlass javax/naming/NamingException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass java/rmi/AlreadyBoundException
instanceKlass java/rmi/NotBoundException
instanceKlass java/net/URISyntaxException
instanceKlass java/sql/SQLException
instanceKlass java/lang/InterruptedException
instanceKlass java/security/GeneralSecurityException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 51 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 428 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 7 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 100 1 10 12 1 9 12 1 1 10 12 1 10 100 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 7 1 10 100 12 1 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 11 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 9 12 1 1 11 100 12 1 1 7 1 10 12 1 10 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
ciMethod java/lang/Throwable setStackTrace ([Ljava/lang/StackTraceElement;)V 0 0 1 0 -1
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 690 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 3 10 10 100 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 100 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 9 12 1 1 7 1 7 1 10 12 1 7 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 1 10 7 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 11 7 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 11 7 1 8 1 10 100 1 11 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 10 11 12 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 7 12 1 1 1 7 1 6 0 10 12 1 1 11 7 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 516 7 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 7 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 7 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 7 1 8 1 10 4 4 10 12 1 1 10 12 1 8 1 4 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 7 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/String 1 1 1443 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 10 12 1 1 3 10 12 1 1 7 1 11 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 100 1 100 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 11 10 7 12 1 1 11 12 1 11 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 10 12 1 100 1 10 10 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 12 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 10 12 10 7 12 1 1 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 7 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 7 1 8 1 10 10 10 12 1 10 12 1 1 8 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 100 1 100 1 10 12 100 1 10 10 100 12 1 1 1 100 1 10 100 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 7 12 1 1 10 7 12 1 1 10 7 12 1 1 8 1 10 12 1 10 12 1 10 9 12 1 10 12 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 7 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciInstanceKlass java/lang/constant/ConstantDesc 1 0 37 100 1 100 1 1 1 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/InternalError 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass sun/security/ssl/SSLSocketImpl$AppInputStream
instanceKlass sun/nio/ch/ChannelInputStream
instanceKlass com/mysql/cj/protocol/a/CompressedInputStream
instanceKlass com/mysql/cj/protocol/ReadAheadInputStream
instanceKlass java/io/ObjectInputStream$PeekInputStream
instanceKlass java/io/ObjectInputStream$BlockDataInputStream
instanceKlass java/io/ObjectInputStream
instanceKlass sun/nio/ch/NioSocketImpl$1
instanceKlass java/net/Socket$SocketInputStream
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 195 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 3 7 1 8 1 10 10 100 12 1 1 1 7 1 10 11 7 12 1 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 100 1 10 7 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 100 12 1 1 1 7 1 5 0 10 12 1 100 1 7 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1287 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 7 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 7 1 9 7 1 9 7 1 9 9 7 1 9 7 1 9 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 100 12 1 1 8 1 100 1 11 12 1 1 8 1 11 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
instanceKlass com/intellij/database/remote/jdbc/impl/JdbcClassLoader$1
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1108 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 12 1 10 7 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 12 1 8 1 10 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 7 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 100 1 10 12 1 1 7 1 7 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 7 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 11 7 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 439 10 100 12 1 1 1 10 100 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 7 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 400 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 7 1 10 7 12 1 1 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 7 1 100 1 8 1 10 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 1 8 1 10 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 100 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 8 1 10 100 12 1 1 1 8 1 10 7 12 1 1 1 9 12 1 7 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 581 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 12 1 7 1 8 1 10 12 1 8 1 11 100 12 1 1 1 7 1 11 7 12 1 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 1 9 12 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Member 1 1 37 100 1 10 12 1 1 100 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass java/util/concurrent/ForkJoinWorkerThread
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
instanceKlass java/lang/BaseVirtualThread
ciInstanceKlass java/lang/Thread 1 1 870 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 10 12 1 10 100 12 1 1 100 1 8 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 9 12 1 1 10 12 1 7 1 10 12 1 100 1 8 1 10 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 3 8 1 7 1 5 0 10 7 12 1 1 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 1 8 1 10 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 8 1 9 7 12 1 1 9 12 1 1 5 0 100 1 10 100 1 10 100 1 10 7 1 10 8 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 7 1 9 12 1 1 100 1 10 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 10 12 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 10 12 9 12 1 1 10 12 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 10 10 12 1 10 12 1 1 9 12 1 9 12 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 8 1 10 9 12 1 10 12 1 7 1 8 1 10 10 12 1 8 1 10 12 1 1 9 12 10 12 8 1 10 10 12 1 10 12 1 8 1 10 12 1 10 8 1 10 100 12 1 1 10 12 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 10 12 1 100 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Thread NEW_THREAD_BINDINGS Ljava/lang/Object; java/lang/Class
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciMethod java/lang/Thread getContextClassLoader ()Ljava/lang/ClassLoader; 512 0 425 0 -1
ciInstanceKlass java/lang/Runnable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/net/URL 1 1 771 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 10 100 1 10 10 12 1 8 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 7 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 9 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 8 1 7 1 10 10 7 12 1 1 1 10 12 1 8 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 7 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/Module 1 1 1070 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 10 12 1 10 7 12 1 1 8 1 8 1 10 8 1 8 1 9 12 1 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 11 12 1 9 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 11 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 7 1 11 12 1 7 1 7 1 10 12 1 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 11 7 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 1 7 1 8 1 10 12 1 1 100 1 11 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 7 1 7 1 10 12 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 7 12 1 1 8 1 18 12 1 1 100 1 100 1 9 12 1 1 9 12 1 9 12 1 11 100 12 1 1 1 100 1 11 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 7 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 16 1 15 10 12 16 15 10 7 12 1 1 1 15 10 100 12 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
ciInstanceKlass java/lang/Character 1 1 604 7 1 7 1 100 1 9 12 1 1 8 1 9 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Integer 1 1 453 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
instanceKlass java/math/BigDecimal
instanceKlass java/util/concurrent/atomic/Striped64
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Thread$FieldHolder 1 1 48 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/Thread$Constants 0 0 59 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 7 1 8 1 10 12 1 9 7 12 1 1 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadGroup 1 1 411 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 18 12 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 1 11 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 11 12 1 11 12 1 1 100 1 10 10 12 1 100 1 10 18 12 1 1 11 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 11 12 10 12 1 1 10 12 1 1 11 7 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 8 1 10 8 1 10 12 1 10 12 1 8 1 9 12 1 1 9 12 1 10 100 12 1 1 1 100 9 12 1 1 7 1 9 12 1 10 12 10 12 1 1 100 10 12 9 12 1 10 12 1 100 1 10 11 12 1 1 7 1 10 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ThreadGroup $assertionsDisabled Z 1
ciInstanceKlass java/lang/Thread$UncaughtExceptionHandler 1 0 16 100 1 100 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 374 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 10 12 1 11 7 12 1 1 1 11 12 1 11 12 1 11 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 7 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 10 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
instanceKlass java/lang/ThreadBuilders$BoundVirtualThread
instanceKlass java/lang/VirtualThread
ciInstanceKlass java/lang/BaseVirtualThread 0 0 36 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 1
ciInstanceKlass java/lang/VirtualThread 0 0 907 9 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 100 1 10 12 1 9 12 1 1 18 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 10 12 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 9 12 1 1 9 12 1 100 1 10 10 12 1 10 100 12 1 1 10 9 10 10 12 1 1 10 12 1 1 10 100 12 1 1 10 100 1 10 9 10 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 7 1 9 12 1 1 10 7 12 1 1 10 9 12 1 1 18 9 100 12 1 1 1 11 100 12 1 1 1 11 100 1 11 12 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 11 100 12 1 1 10 12 9 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 9 12 1 9 12 1 7 1 10 10 12 1 1 10 12 1 10 12 7 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 10 12 1 10 12 1 10 7 12 1 1 8 1 8 1 10 9 100 12 1 1 1 10 12 1 1 10 12 1 10 10 10 12 9 12 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 1 18 12 1 1 18 12 1 10 7 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 18 12 1 10 100 12 1 1 1 100 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 100 12 1 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 1 18 12 1 1 5 0 9 12 1 10 12 1 18 12 1 100 1 10 12 10 7 12 1 1 10 12 1 1 7 1 8 1 10 10 12 1 10 12 1 1 10 12 1 9 12 1 8 10 12 1 1 8 8 9 12 1 8 10 12 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 15 16 15 10 12 16 15 10 12 16 16 15 10 12 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1
ciInstanceKlass java/lang/ThreadBuilders$BoundVirtualThread 0 0 132 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 9 100 12 1 1 1 10 12 1 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/ContinuationScope 0 0 50 10 100 12 1 1 1 10 100 12 1 1 1 100 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/StackChunk 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Float 1 1 279 7 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 4 7 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 1 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 3 3 100 1 4 4 4 3 10 12 1 1 9 12 1 1 100 1 10 3 3 4 4 10 12 1 3 3 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 4 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Float $assertionsDisabled Z 1
ciInstanceKlass java/lang/Double 1 1 290 7 1 7 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 12 1 1 10 100 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 1 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 6 0 1 6 0 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 213 7 1 100 1 10 7 12 1 1 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 222 7 1 7 1 100 1 10 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Long 1 1 524 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 100 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 7 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 10 12 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 573 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/FillerObject 0 0 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 190 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 8 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 7 1 100 1 10 12 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 7 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
ciMethod java/lang/ref/Reference get ()Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 548 0 66103 0 -1
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass sun/util/resources/Bundles$BundleReference
instanceKlass sun/security/util/MemoryCache$SoftCacheEntry
instanceKlass java/util/ResourceBundle$BundleReference
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
instanceKlass java/io/ClassCache$CacheRef
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass javax/crypto/JceSecurity$WeakIdentityWrapper
instanceKlass java/util/ResourceBundle$KeyElementReference
instanceKlass sun/rmi/transport/WeakRef
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass jdk/internal/util/WeakReferenceKey
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 50 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1
instanceKlass com/mysql/cj/jdbc/AbandonedConnectionCleanupThread$ConnectionFinalizerPhantomReference
instanceKlass sun/rmi/transport/DGCClient$EndpointEntry$RefEntry$PhantomLiveRef
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 155 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 10 12 1 7 1 8 1 10 12 1 10 12 1 1 9 12 1 100 1 10 12 1 7 1 11 100 12 1 1 10 12 1 7 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 7 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer ENABLED Z 1
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$Type
instanceKlass com/mysql/cj/QueryReturnType
instanceKlass com/mysql/cj/Query$CancelStatus
instanceKlass com/mysql/cj/MysqlType
instanceKlass com/mysql/cj/protocol/a/authentication/AuthenticationWebAuthnClient$AuthStage
instanceKlass com/mysql/cj/protocol/a/authentication/CachingSha2PasswordPlugin$AuthStage
instanceKlass javax/net/ssl/SSLEngineResult$HandshakeStatus
instanceKlass sun/security/ssl/X509Authentication
instanceKlass sun/security/ssl/Finished$VerifyDataScheme
instanceKlass sun/security/rsa/RSAUtil$KeyType
instanceKlass sun/security/ssl/SSLTrafficKeyDerivation$KeySchedule
instanceKlass sun/security/ssl/SSLTrafficKeyDerivation
instanceKlass sun/security/ssl/SSLSecretDerivation$SecretSchedule
instanceKlass sun/security/ssl/ContentType
instanceKlass sun/security/ssl/SSLKeyExchange$T12KeyAgreement
instanceKlass sun/security/ssl/PskKeyExchangeModesExtension$PskKeyExchangeMode
instanceKlass sun/security/ssl/ECPointFormatsExtension$ECPointFormat
instanceKlass sun/security/ssl/CertStatusExtension$CertStatusRequestType
instanceKlass sun/security/ssl/SSLExtension
instanceKlass sun/security/ssl/SignatureScheme$SigAlgParamSpec
instanceKlass sun/security/ssl/SignatureScheme
instanceKlass sun/security/ssl/ClientAuthType
instanceKlass sun/security/ssl/SSLHandshake
instanceKlass sun/security/ssl/NamedGroup
instanceKlass sun/security/ssl/NamedGroup$NamedGroupSpec
instanceKlass sun/security/ssl/CipherSuite$KeyExchange
instanceKlass sun/security/ssl/CipherSuite$MacAlg
instanceKlass sun/security/ssl/CipherSuite$HashAlg
instanceKlass java/nio/file/AccessMode
instanceKlass sun/security/ssl/CipherType
instanceKlass sun/security/ssl/SSLCipher
instanceKlass sun/security/ssl/CipherSuite
instanceKlass java/security/CryptoPrimitive
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraint$Operator
instanceKlass sun/security/ssl/ProtocolVersion
instanceKlass com/mysql/cj/protocol/a/NativeConstants$StringSelfDataType
instanceKlass com/mysql/cj/protocol/a/NativeConstants$StringLengthDataType
instanceKlass com/mysql/cj/protocol/a/NativeConstants$IntegerDataType
instanceKlass com/mysql/cj/protocol/Resultset$Concurrency
instanceKlass com/mysql/cj/protocol/Resultset$Type
instanceKlass com/mysql/cj/conf/BooleanPropertyDefinition$AllowableValues
instanceKlass com/mysql/cj/conf/PropertyDefinitions$Compression
instanceKlass com/mysql/cj/conf/PropertyDefinitions$AuthMech
instanceKlass com/mysql/cj/conf/PropertyDefinitions$XdevapiSslMode
instanceKlass com/mysql/cj/conf/PropertyDefinitions$ZeroDatetimeBehavior
instanceKlass com/mysql/cj/conf/PropertyDefinitions$SslMode
instanceKlass com/mysql/cj/conf/PropertyDefinitions$DatabaseTerm
instanceKlass java/math/RoundingMode
instanceKlass com/mysql/cj/util/SearchMode
instanceKlass com/mysql/cj/conf/PropertyKey
instanceKlass com/mysql/cj/conf/ConnectionUrl$HostsCardinality
instanceKlass com/mysql/cj/conf/ConnectionUrl$Type
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelperImpl$PropertyReplacement$Direction
instanceKlass com/intellij/database/remote/jdbc/impl/UnparsedValueKind
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelper$LikeSupport
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcHelper$Case
instanceKlass com/intellij/database/remote/jdbc/helpers/JdbcSettings$SslMode
instanceKlass java/time/format/TextStyle
instanceKlass java/time/format/DateTimeFormatterBuilder$SettingsParser
instanceKlass java/time/format/ResolverStyle
instanceKlass java/util/Locale$Category
instanceKlass java/time/format/SignStyle
instanceKlass java/time/temporal/JulianFields$Field
instanceKlass java/time/temporal/IsoFields$Unit
instanceKlass java/time/temporal/IsoFields$Field
instanceKlass java/time/temporal/ChronoUnit
instanceKlass java/time/temporal/ChronoField
instanceKlass java/net/Proxy$Type
instanceKlass java/net/StandardProtocolFamily
instanceKlass jdk/internal/util/OperatingSystem
instanceKlass java/io/ObjectInputFilter$Status
instanceKlass java/security/DrbgParameters$Capability
instanceKlass sun/security/util/KnownOIDs
instanceKlass sun/util/logging/PlatformLogger$Level
instanceKlass java/lang/System$Logger$Level
instanceKlass java/lang/StackWalker$Option
instanceKlass java/util/stream/Collector$Characteristics
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/lang/annotation/RetentionPolicy
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccess
instanceKlass com/jetbrains/Extensions
instanceKlass com/intellij/database/remote/toolkit/RemoteDesktopActionsHandlerImpl$State
instanceKlass java/security/Provider$OPType
instanceKlass jdk/internal/logger/BootstrapLogger$LoggingBackend
instanceKlass java/util/stream/StreamShape
instanceKlass java/util/stream/StreamOpFlag$Type
instanceKlass java/util/stream/StreamOpFlag
instanceKlass java/util/regex/Pattern$Qtype
instanceKlass java/util/zip/ZipCoder$Comparison
instanceKlass java/nio/file/LinkOption
instanceKlass sun/nio/fs/WindowsPathType
instanceKlass java/nio/file/StandardOpenOption
instanceKlass java/util/concurrent/TimeUnit
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassOption
instanceKlass java/lang/invoke/VarHandle$AccessType
instanceKlass java/lang/invoke/VarHandle$AccessMode
instanceKlass java/lang/invoke/MethodHandleImpl$Intrinsic
instanceKlass java/lang/invoke/LambdaForm$BasicType
instanceKlass java/lang/invoke/LambdaForm$Kind
instanceKlass sun/invoke/util/Wrapper
instanceKlass java/io/File$PathStatus
instanceKlass java/lang/module/ModuleDescriptor$Requires$Modifier
instanceKlass java/lang/reflect/AccessFlag$Location
instanceKlass java/lang/reflect/AccessFlag
instanceKlass java/lang/module/ModuleDescriptor$Modifier
instanceKlass java/lang/reflect/ClassFileFormatVersion
instanceKlass java/lang/Thread$State
ciInstanceKlass java/lang/Enum 1 1 204 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 10 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 100 1 8 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 100 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 472 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 7 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 1 1 457 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 7 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 7 1 9 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 12 1 1 11 7 1 10 12 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 10 100 12 1 1 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Parameter 0 0 243 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 11 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 12 1 10 12 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 0 0 196 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/StringBuffer 1 1 483 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 100 12 1 1 1 10 10 12 1 1 9 12 1 1 10 100 12 1 1 10 100 1 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 9 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
instanceKlass jdk/internal/loader/BuiltinClassLoader
instanceKlass java/net/URLClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass com/intellij/database/remote/jdbc/impl/JdbcClassLoader$JdbcClassLoaderImpl
ciInstanceKlass java/net/URLClassLoader 1 1 600 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 7 12 1 1 10 12 1 11 12 1 11 12 1 1 11 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 12 1 1 10 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 1 8 1 10 100 1 10 12 1 10 7 12 1 100 1 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/util/jar/Manifest 1 1 339 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 7 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 1 10 9 100 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 10 12 1 11 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 117 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 3 10 100 1 10 100 12 1 1 1 9 12 1 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/io/ByteArrayInputStream $assertionsDisabled Z 1
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 256 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer IOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield java/nio/Buffer $assertionsDisabled Z 1
instanceKlass sun/security/jca/ProviderList$ServiceList
instanceKlass sun/security/jca/ProviderList$3
instanceKlass java/util/Collections$SingletonList
instanceKlass java/util/Collections$CopiesList
instanceKlass java/util/Vector
instanceKlass java/util/AbstractSequentialList
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/ArrayList$SubList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 7 1 11 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 100 1 10 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 7 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Iterable 1 1 62 10 7 12 1 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Collection 1 1 115 11 100 12 1 1 1 100 1 11 7 12 1 1 1 10 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 10 100 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/SequencedCollection 1 1 109 100 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 8 1
ciInstanceKlass java/util/List 1 1 251 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 10 100 12 1 1 1 11 12 1 1 11 12 1 11 12 1 100 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 7 12 1 1 1 7 1 10 12 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
instanceKlass it/unimi/dsi/fastutil/ints/AbstractIntCollection
instanceKlass it/unimi/dsi/fastutil/objects/AbstractObjectCollection
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/AbstractQueue
instanceKlass java/util/HashMap$Values
instanceKlass java/util/IdentityHashMap$Values
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 100 12 1 1 1 11 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/time/DateTimeException
instanceKlass java/security/ProviderException
instanceKlass java/util/MissingResourceException
instanceKlass java/util/NoSuchElementException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass com/mysql/cj/exceptions/CJException
instanceKlass java/lang/SecurityException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass org/bouncycastle/crypto/CryptoServiceConstraintsException
instanceKlass java/lang/IllegalStateException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/misc/ThreadTracker$ThreadRef
instanceKlass java/nio/DirectByteBuffer$Deallocator
instanceKlass jdk/net/UnixDomainPrincipal
instanceKlass com/jetbrains/internal/JBRApi$DynamicCallTargetKey
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass com/jetbrains/internal/AccessContext$DynamicCallTarget
instanceKlass com/jetbrains/internal/ASMUtils$InternalMethodInfo
instanceKlass com/jetbrains/internal/Mapping$Method
instanceKlass com/jetbrains/internal/Proxy$Info$StaticMethod
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$ProxyClassContext
instanceKlass com/jetbrains/internal/ProxyRepository$Key
instanceKlass com/jetbrains/internal/ProxyRepository$Registry$StaticKey
instanceKlass com/jetbrains/internal/ProxyRepository$Registry$StaticValue
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass jdk/internal/reflect/ReflectionFactory$Config
instanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs
instanceKlass jdk/internal/foreign/abi/VMStorage
ciInstanceKlass java/lang/Record 1 1 22 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 780 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 7 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 10 12 1 1 10 12 1 1 7 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 7 1 10 10 12 1 1 7 1 7 1 9 12 1 1 7 1 7 1 7 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 7 1 1 7 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljdk/internal/util/ReferencedKeySet; jdk/internal/util/ReferencedKeySet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfMethod 1 0 43 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 733 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 10 9 7 12 1 1 1 9 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 100 1 11 12 1 10 100 1 11 12 1 7 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 10 7 12 1 1 10 12 1 1 100 1 100 1 8 1 8 1 10 10 12 1 1 10 12 1 10 12 1 7 1 10 100 12 1 1 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 7 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 7 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 7 12 1 1 1 10 12 3 10 7 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 7 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 7 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 7 1 7 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 4
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
instanceKlass java/util/Collections$SingletonMap
instanceKlass java/util/concurrent/ConcurrentSkipListMap
instanceKlass java/util/EnumMap
instanceKlass java/util/TreeMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass java/util/HashMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 196 10 7 12 1 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 100 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 100 1 10 11 11 12 1 1 11 12 1 7 1 100 1 11 12 1 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1
ciInstanceKlass java/util/concurrent/ConcurrentMap 1 1 208 11 7 12 1 1 1 10 100 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 100 1 11 100 12 1 1 1 18 12 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 100 12 1 11 12 1 1 11 12 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 7 12 1 1 7 1 10 7 12 1 1 1 10 12 1 100 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 10 11 12 1 1 11 10 12 1 1 7 1 10 12 1 10 7 12 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 11 12 1 100 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 1 11 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 7 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackTraceElement 0 0 235 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 100 12 1 1 1 7 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 7 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/Continuation 0 0 549 9 100 12 1 1 1 9 12 1 9 12 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 11 100 12 1 1 1 10 7 1 9 12 1 1 9 12 1 1 10 8 1 10 12 1 9 12 1 1 10 11 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 100 1 10 12 1 11 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 1 9 12 1 1 11 12 1 1 9 12 1 1 8 1 10 11 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 11 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 11 7 12 1 1 10 7 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 8 1 10 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 9 12 1 1 8 1 10 7 12 1 1 10 100 12 1 1 8 1 8 1 10 12 10 100 12 1 1 1 10 7 1 10 7 12 1 1 1 18 11 100 12 1 1 1 18 12 1 11 12 1 1 7 1 10 7 12 1 1 10 12 1 1 8 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 8 1 10 12 1 7 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 1 15 10 12 16 15 11 7 12 1 1 1 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
ciInstanceKlass java/lang/invoke/MethodHandleStatics 1 1 320 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 8 1 8 1 10 12 1 10 100 12 1 1 1 10 7 12 1 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 100 1 10 10 12 1 1 100 1 10 10 12 1 7 1 7 1 8 1 8 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 7 12 1 1 1 9 12 1 8 1 8 1 8 1 8 1 9 12 1 8 1 9 12 1 8 1 8 1 9 12 1 8 1 8 1 9 12 1 8 1 9 12 1 8 1 9 12 1 8 1 8 1 9 12 1 8 1 8 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MethodHandleStatics UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/lang/invoke/MethodHandleStatics CLASSFILE_VERSION I 65
staticfield java/lang/invoke/MethodHandleStatics DEBUG_METHOD_HANDLE_NAMES Z 0
staticfield java/lang/invoke/MethodHandleStatics TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/MethodHandleStatics TRACE_METHOD_LINKAGE Z 0
staticfield java/lang/invoke/MethodHandleStatics TRACE_RESOLVE Z 0
staticfield java/lang/invoke/MethodHandleStatics COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/MethodHandleStatics LOG_LF_COMPILATION_FAILURE Z 0
staticfield java/lang/invoke/MethodHandleStatics DONT_INLINE_THRESHOLD I 30
staticfield java/lang/invoke/MethodHandleStatics PROFILE_LEVEL I 0
staticfield java/lang/invoke/MethodHandleStatics PROFILE_GWT Z 1
staticfield java/lang/invoke/MethodHandleStatics CUSTOMIZE_THRESHOLD I 127
staticfield java/lang/invoke/MethodHandleStatics VAR_HANDLE_GUARDS Z 1
staticfield java/lang/invoke/MethodHandleStatics MAX_ARITY I 255
staticfield java/lang/invoke/MethodHandleStatics VAR_HANDLE_IDENTITY_ADAPT Z 0
staticfield java/lang/invoke/MethodHandleStatics DUMP_CLASS_FILES Ljdk/internal/util/ClassFileDumper; jdk/internal/util/ClassFileDumper
instanceKlass java/util/LinkedHashMap
ciInstanceKlass java/util/HashMap 1 1 629 10 7 12 1 1 1 7 1 10 12 1 1 7 1 10 7 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 10 7 12 1 1 1 7 1 3 10 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 10 12 1 9 12 1 1 4 10 12 1 10 12 1 1 11 7 12 1 1 9 12 1 1 10 7 12 1 1 1 6 0 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 10 12 1 10 12 1 1 9 12 10 12 1 1 9 7 12 1 1 1 9 12 9 12 1 10 12 1 1 9 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 3 4 10 12 1 1 10 12 1 1 9 12 1 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 7 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 9 12 1 1 7 1 10 9 12 7 1 10 100 1 10 11 7 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 1 1 10 12 1 100 1 7 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 10 7 12 1 1 1 100 1 10 4 4 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 6 0 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 12 1 10 12 1 10 10 12 1 1 6 0 8 1 10 12 1 10 12 7 1 7 1 1 1 1 5 0 1 3 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/HashMap get (Ljava/lang/Object;)Ljava/lang/Object; 822 0 9428 0 -1
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1059 7 1 100 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 9 12 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 8 1 8 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 12 1 1 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 10 12 1 1 7 1 7 1 10 10 12 1 1 10 12 1 1 8 1 8 1 7 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 10 12 1 1 8 1 8 1 8 1 7 1 8 1 7 1 8 1 7 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 7 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 7 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm DEFAULT_CUSTOMIZED Ljava/lang/invoke/MethodHandle; null
staticfield java/lang/invoke/LambdaForm DEFAULT_KIND Ljava/lang/invoke/LambdaForm$Kind; java/lang/invoke/LambdaForm$Kind
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 724 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 7 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 7 1 7 1 10 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 7 1 8 9 7 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 8 1 8 1 7 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 7 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 7 1 10 12 1 10 7 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 8 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 7 12 1 1 1 8 1 7 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 7 1 10 8 1 8 1 8 1 8 1 10 12 1 100 1 100 1 100 1 10 100 1 10 7 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 7 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
instanceKlass java/lang/invoke/VarHandleReferences$FieldStaticReadOnly
instanceKlass java/lang/invoke/VarHandleLongs$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleBooleans$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleByteArrayAsDoubles$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsFloats$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsChars$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsShorts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 473 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 9 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 9 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 10 12 1 1 7 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectMethodHandleAccessor
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 38 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/MethodAccessor 1 0 17 100 1 100 1 1 1 1 100 1 100 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/SerializationConstructorAccessorImpl
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor$NativeAccessor
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstructorAccessor 1 0 16 100 1 100 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 1 1 18 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/Reflection 1 1 385 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 11 7 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 7 1 7 1 11 7 12 1 1 8 1 11 12 1 1 10 100 12 1 1 1 100 1 11 12 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 10 12 8 1 11 12 1 1 9 12 1 1 7 1 8 1 8 1 11 12 1 7 1 7 1 7 1 8 1 11 12 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1
staticfield jdk/internal/reflect/Reflection ALL_MEMBERS Ljava/util/Set; java/util/ImmutableCollections$Set12
ciInstanceKlass jdk/internal/reflect/CallerSensitive 0 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciMethod jdk/internal/reflect/Reflection getCallerClass ()Ljava/lang/Class; 256 0 128 0 -1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 0 0 125 10 7 12 1 1 1 9 7 12 1 1 1 100 1 10 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 142 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 0 0 47 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 11 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/FieldAccessor 1 0 48 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/MethodHandleFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 269 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 8 1 8 1 8 1 10 12 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 0 0 62 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 307 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 100 1 7 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 10 12 10 12 1 1 7 1 7 1 7 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
instanceKlass java/lang/invoke/DirectMethodHandle$StaticAccessor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 923 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 1 1 10 12 10 12 1 7 1 10 12 1 10 12 1 1 8 1 9 12 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 7 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciMethod java/lang/invoke/DirectMethodHandle allocateInstance (Ljava/lang/Object;)Ljava/lang/Object; 512 0 2071 0 -1
ciMethod java/lang/invoke/DirectMethodHandle constructorMethod (Ljava/lang/Object;)Ljava/lang/Object; 512 0 2070 0 -1
ciInstanceKlass java/lang/invoke/DirectMethodHandle$StaticAccessor 1 1 96 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 1 100 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle$StaticAccessor $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/DirectMethodHandle$Accessor 1 1 93 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 1 100 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle$Accessor $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 690 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 1 7 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 7 1 10 12 1 8 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 7 1 7 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/NativeEntryPoint 0 0 194 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 15 10 100 12 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/ABIDescriptor 0 0 55 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/foreign/abi/VMStorage 0 0 91 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 15 15 15 15 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs 0 0 66 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 18 12 1 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 8 1 15 15 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackWalker 1 1 271 9 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 12 1 1 100 1 8 1 10 10 7 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 100 1 8 1 10 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 11 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/StackWalker DEFAULT_EMPTY_OPTION Ljava/util/EnumSet; java/util/RegularEnumSet
staticfield java/lang/StackWalker DEFAULT_WALKER Ljava/lang/StackWalker; java/lang/StackWalker
ciInstanceKlass java/lang/StackWalker$StackFrame 0 0 41 100 1 10 12 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 0 0 142 10 7 12 1 1 1 9 7 12 1 1 1 9 7 1 9 12 1 1 11 100 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 12 1 1 9 12 1 1 10 7 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 7 1 7 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/LiveStackFrame 0 0 135 100 1 10 100 12 1 1 1 11 7 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 12 1 10 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 0 375 100 1 7 1 3 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 10 100 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 100 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 7 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 7 1 10 18 12 1 10 12 1 1 7 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/Invokers$Holder 1 1 128 1 100 1 100 1 1 1 1 1 1 1 7 1 7 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 1 100 1 1 12 9 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 1 100 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 10 12 10 12 10 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1
ciMethod java/lang/invoke/Invokers$Holder linkToTargetMethod (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 512 0 1408 0 -1
ciInstanceKlass java/lang/invoke/DirectMethodHandle$Holder 1 1 548 1 100 1 100 1 1 1 1 1 1 1 7 1 1 12 10 1 12 10 1 7 1 7 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 12 10 12 10 1 1 12 10 12 10 1 1 12 10 1 1 12 10 12 10 12 10 12 10 1 1 12 10 12 10 1 1 12 10 12 10 1 1 12 10 1 1 12 10 12 10 12 10 12 10 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 12 10 12 10 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 12 10 12 10 1 1 12 10 1 12 10 12 10 1 1 12 10 1 12 10 12 10 1 1 1 12 10 1 12 10 1 7 1 1 12 9 1 7 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 1 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1
ciMethod java/lang/invoke/DirectMethodHandle$Holder newInvokeSpecial (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 608 0 869 0 -1
ciInstanceKlass java/util/ArrayList 1 1 509 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 11 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 7 1 10 12 1 10 10 7 12 1 1 1 10 7 12 1 1 10 12 1 100 1 10 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 7 1 10 7 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/RandomAccess 1 0 7 100 1 100 1 1 1
ciInstanceKlass java/lang/Class$1 1 1 47 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 12 1 1 1
ciInstanceKlass java/security/PrivilegedAction 1 0 14 100 1 100 1 1 1 1 1 1 1 1 1 1
ciMethod java/security/PrivilegedAction run ()Ljava/lang/Object; 0 0 1 0 -1
ciInstanceKlass java/lang/annotation/Annotation 1 0 17 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/security/PrivilegedExceptionAction 1 0 17 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadLocal$ThreadLocalMap 1 1 143 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 7 1 9 12 1 1 9 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/ThreadLocal$ThreadLocalMap set (Ljava/lang/ThreadLocal;Ljava/lang/Object;)V 512 0 530 0 -1
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$Sync$ThreadLocalHoldCounter
instanceKlass jdk/internal/math/FloatingDecimal$1
instanceKlass jdk/internal/jimage/ImageBufferCache$1
instanceKlass jdk/internal/misc/CarrierThreadLocal
ciInstanceKlass java/lang/ThreadLocal 1 1 332 9 7 12 1 1 1 3 10 7 12 1 1 1 100 1 10 12 1 1 10 7 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 100 1 100 1 10 10 12 1 10 12 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 10 12 1 1 10 12 1 9 12 1 1 10 12 10 12 1 100 1 10 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 9 100 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 18 12 1 1 10 12 1 1 7 1 7 1 10 12 1 9 7 12 1 1 1 10 100 12 1 1 1 11 12 1 1 11 7 12 1 1 11 12 1 7 1 8 1 11 12 1 1 10 12 1 1 11 100 12 1 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 10 12 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ThreadLocal TRACE_VTHREAD_LOCALS Z 0
staticfield java/lang/ThreadLocal nextHashCode Ljava/util/concurrent/atomic/AtomicInteger; java/util/concurrent/atomic/AtomicInteger
staticfield java/lang/ThreadLocal $assertionsDisabled Z 1
ciMethod java/lang/ThreadLocal get ()Ljava/lang/Object; 254 0 166 0 -1
ciMethod java/lang/ThreadLocal set (Ljava/lang/Object;)V 512 0 517 0 -1
ciMethod java/lang/ThreadLocal getMap (Ljava/lang/Thread;)Ljava/lang/ThreadLocal$ThreadLocalMap; 516 0 4081 0 -1
ciMethod java/lang/ThreadLocal createMap (Ljava/lang/Thread;Ljava/lang/Object;)V 10 0 6 0 -1
ciMethod java/lang/ThreadLocal get (Ljava/lang/Thread;)Ljava/lang/Object; 512 0 3543 0 -1
ciMethod java/lang/ThreadLocal set (Ljava/lang/Thread;Ljava/lang/Object;)V 512 0 519 0 -1
ciInstanceKlass java/lang/invoke/DirectMethodHandle$Constructor 1 1 91 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle$Constructor $assertionsDisabled Z 1
instanceKlass java/util/LinkedHashMap$Entry
ciInstanceKlass java/util/HashMap$Node 1 1 95 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 1 7 1 11 12 1 1 10 12 1 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1
ciInstanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry 1 1 37 10 7 12 1 1 1 9 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
ciInstanceKlass java/lang/invoke/BoundMethodHandle$Species_LL 1 1 73 1 7 1 7 1 1 1 1 1 1 1 1 12 9 1 1 1 12 10 12 9 12 9 1 1 12 10 1 1 1 100 1 7 1 1 12 10 1 7 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 1
ciInstanceKlass java/util/zip/ZipFile$Source$Key 1 1 84 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 100 1 5 0 11 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 12 1 1 10 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
ciInstanceKlass java/util/logging/Level 1 1 294 10 7 12 1 1 1 10 12 1 10 7 12 1 1 100 1 10 9 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 7 1 18 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 18 100 1 18 12 1 10 12 1 8 1 3 9 12 1 8 1 9 12 8 1 9 12 8 1 9 12 8 1 9 12 8 1 9 12 8 1 9 12 8 1 9 12 8 1 3 9 12 9 12 1 1 7 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 1 1 16 1 15 10 12 1 8 1 15 10 7 12 1 1 1 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/util/logging/Level OFF Ljava/util/logging/Level; java/util/logging/Level
staticfield java/util/logging/Level SEVERE Ljava/util/logging/Level; java/util/logging/Level
staticfield java/util/logging/Level WARNING Ljava/util/logging/Level; java/util/logging/Level
staticfield java/util/logging/Level INFO Ljava/util/logging/Level; java/util/logging/Level
staticfield java/util/logging/Level CONFIG Ljava/util/logging/Level; java/util/logging/Level
staticfield java/util/logging/Level FINE Ljava/util/logging/Level; java/util/logging/Level
staticfield java/util/logging/Level FINER Ljava/util/logging/Level; java/util/logging/Level
staticfield java/util/logging/Level FINEST Ljava/util/logging/Level; java/util/logging/Level
staticfield java/util/logging/Level ALL Ljava/util/logging/Level; java/util/logging/Level
staticfield java/util/logging/Level standardLevels [Ljava/util/logging/Level; 9 [Ljava/util/logging/Level;
ciMethod java/util/logging/Level intValue ()I 256 0 128 0 -1
instanceKlass java/util/logging/LogManager$RootLogger
ciInstanceKlass java/util/logging/Logger 1 1 742 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 10 7 12 1 1 1 7 1 8 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 7 1 10 9 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 12 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 11 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 9 7 12 1 1 1 8 1 10 12 1 1 8 1 10 12 1 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 8 1 8 1 9 12 1 10 12 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 7 10 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 9 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 11 100 12 1 1 1 100 1 18 12 1 1 10 7 12 1 1 1 10 12 1 100 1 18 12 1 1 10 10 12 1 1 9 12 1 18 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 100 1 10 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 11 12 1 7 1 10 12 1 11 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 9 12 1 10 12 1 8 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 16 8 1 8 1 15 10 100 12 1 1 1 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/util/logging/Logger emptyHandlers [Ljava/util/logging/Handler; 0 [Ljava/util/logging/Handler;
staticfield java/util/logging/Logger offValue I 2147483647
staticfield java/util/logging/Logger SYSTEM_BUNDLE Ljava/util/logging/Logger$LoggerBundle; java/util/logging/Logger$LoggerBundle
staticfield java/util/logging/Logger NO_RESOURCE_BUNDLE Ljava/util/logging/Logger$LoggerBundle; java/util/logging/Logger$LoggerBundle
staticfield java/util/logging/Logger treeLock Ljava/lang/Object; java/lang/Object
staticfield java/util/logging/Logger global Ljava/util/logging/Logger; java/util/logging/Logger
ciMethod java/util/logging/Logger isLoggable (Ljava/util/logging/Level;)Z 536 0 3056 0 -1
ciInstanceKlass java/util/logging/Logger$ConfigurationData 1 1 144 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 100 12 1 1 7 1 8 1 10 12 1 9 12 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/HashMap getNode (Ljava/lang/Object;)Ljava/util/HashMap$Node; 828 12 6456 0 -1
ciMethod java/lang/Thread currentThread ()Ljava/lang/Thread; 0 0 1 0 -1
ciMethod jdk/internal/misc/Unsafe allocateInstance (Ljava/lang/Class;)Ljava/lang/Object; 256 0 128 0 -1
ciMethod java/util/Map get (Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/security/AccessController checkContext (Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/security/AccessControlContext; 512 0 1185 0 -1
ciMethod java/security/AccessController doPrivileged (Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;)Ljava/lang/Object; 28 0 584 0 -1
ciMethod java/security/AccessController doPrivileged (Ljava/security/PrivilegedAction;Ljava/security/AccessControlContext;)Ljava/lang/Object; 512 0 601 0 -1
ciMethod java/security/AccessController ensureMaterializedForStackWalk (Ljava/lang/Object;)V 258 0 129 0 -1
ciMethod java/security/AccessController executePrivileged (Ljava/security/PrivilegedAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object; 238 0 1980 0 -1
ciMethod java/lang/Object <init> ()V 1024 0 170321 0 -1
ciInstanceKlass org/bouncycastle/util/Properties$1 1 1 32 1 1 1 1 1 1 1 1 1 1 1 1 7 100 7 100 7 1 1 1 1 12 12 12 12 9 10 10 1 1 1
ciInstanceKlass com/intellij/execution/rmi/IdeaWatchdogImpl 1 1 86 7 1 5 0 5 0 10 12 1 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
instanceKlass java/nio/charset/CharacterCodingException
instanceKlass javax/security/sasl/SaslException
instanceKlass java/io/EOFException
instanceKlass java/net/SocketException
instanceKlass java/net/UnknownHostException
instanceKlass java/io/UnsupportedEncodingException
instanceKlass java/util/InvalidPropertiesFormatException
instanceKlass java/io/ObjectStreamException
instanceKlass java/nio/file/FileSystemException
instanceKlass java/net/MalformedURLException
instanceKlass java/rmi/RemoteException
ciInstanceKlass java/io/IOException 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/rmi/Remote 1 0 7 100 1 100 1 1 1
ciInstanceKlass jdk/internal/jimage/ImageBufferCache$1 1 1 34 100 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass com/jetbrains/internal/ProxyRepository$Key 1 1 89 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 100 1 10 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1
instanceKlass java/rmi/server/UnicastRemoteObject
instanceKlass sun/rmi/registry/RegistryImpl
ciInstanceKlass java/rmi/server/RemoteServer 1 1 66 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass sun/rmi/registry/RegistryImpl 1 1 671 7 1 8 1 10 7 12 1 1 1 10 7 1 10 100 12 1 1 1 11 100 12 1 1 1 8 1 8 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 10 12 1 1 18 12 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 100 1 10 12 1 1 100 1 10 12 1 100 1 100 1 18 12 1 8 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 100 1 7 1 9 12 1 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 7 1 10 9 12 1 1 10 12 1 1 10 12 1 1 7 1 100 1 10 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 1 100 1 10 10 12 1 100 1 100 1 9 12 1 10 12 1 1 100 1 18 10 100 1 10 12 1 18 12 1 100 1 18 9 12 1 1 8 1 10 100 12 1 1 1 100 1 18 10 12 1 18 100 1 10 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 100 1 100 1 11 12 1 1 100 1 9 12 1 1 11 100 12 1 1 9 100 12 1 1 1 11 100 12 1 1 1 5 0 9 12 1 11 12 1 1 10 100 12 1 1 11 12 1 5 0 100 1 10 12 1 1 100 1 100 1 100 1 100 1 100 1 9 12 1 100 1 10 10 12 1 1 8 1 8 1 10 12 1 1 100 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 100 1 10 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 9 12 1 100 1 5 0 10 12 1 1 100 1 100 1 9 12 1 1 8 1 10 12 1 100 1 10 100 12 1 1 1 10 100 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 100 1 10 10 12 1 100 1 8 1 8 1 10 12 1 100 1 8 1 10 8 1 100 1 8 1 8 1 10 100 1 100 1 100 1 10 12 1 10 12 1 100 1 10 12 1 7 1 10 18 12 1 1 1 1 1 5 0 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 16 15 10 12 8 1 8 1 8 1 8 1 8 1 8 1 16 15 10 12 16 15 10 100 12 1 1 15 10 7 12 1 1 1 1 1 1 100 1 100 1 1
staticfield sun/rmi/registry/RegistryImpl registryFilter Ljava/io/ObjectInputFilter; null
ciInstanceKlass sun/rmi/server/Dispatcher 1 0 12 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass sun/rmi/server/UnicastServerRef 1 1 622 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 10 12 1 1 7 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 7 1 10 100 12 1 1 1 9 12 1 11 11 12 1 1 10 12 1 1 9 12 1 1 100 1 11 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 100 1 100 1 8 1 10 10 12 1 1 11 12 1 11 12 1 8 1 10 12 1 11 12 1 1 7 1 10 12 1 10 7 12 1 1 1 11 12 1 7 1 8 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 100 1 100 1 8 1 10 12 1 1 100 1 10 12 1 1 11 12 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 100 1 8 1 10 100 1 10 12 1 1 100 1 100 1 8 1 10 12 1 100 1 100 1 8 1 10 9 12 1 10 12 1 11 100 12 1 1 1 8 1 10 12 1 7 1 18 12 1 1 10 7 12 1 1 1 8 1 10 7 12 1 1 1 11 7 1 10 12 1 1 10 12 1 11 12 1 1 18 12 1 1 11 12 1 1 100 1 10 12 1 1 10 12 1 9 12 1 1 9 7 12 1 1 1 10 12 1 1 10 100 1 8 1 11 10 12 1 10 100 12 1 1 10 100 12 1 1 18 12 1 10 12 1 1 9 12 1 8 1 18 12 1 18 10 12 1 9 12 1 9 100 12 1 1 1 10 100 12 1 1 10 100 12 1 1 1 10 100 1 10 18 12 1 10 12 10 12 1 1 8 1 10 12 1 1 8 1 10 7 12 1 1 1 10 12 1 8 1 8 1 18 12 1 10 12 1 1 9 12 1 8 1 8 1 10 12 1 1 18 18 7 1 10 7 1 10 10 7 12 1 1 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 1 15 10 12 16 1 8 1 8 1 8 1 8 1 8 1 15 10 12 16 15 10 12 15 10 12 15 10 7 12 1 1 1 15 10 100 12 1 1 1 1 100 1 100 1 1
staticfield sun/rmi/server/UnicastServerRef logCalls Z 0
staticfield sun/rmi/server/UnicastServerRef callLog Lsun/rmi/runtime/Log; sun/rmi/runtime/Log$LoggerLog
staticfield sun/rmi/server/UnicastServerRef wantExceptionLog Z 0
staticfield sun/rmi/server/UnicastServerRef suppressStackTraces Z 0
staticfield sun/rmi/server/UnicastServerRef hashToMethod_Maps Lsun/rmi/server/WeakClassHashMap; sun/rmi/server/UnicastServerRef$HashToMethod_Maps
staticfield sun/rmi/server/UnicastServerRef withoutSkeletons Ljava/util/Map; java/util/Collections$SynchronizedMap
instanceKlass sun/rmi/runtime/Log$LoggerLog
ciInstanceKlass sun/rmi/runtime/Log 1 1 201 10 7 12 1 1 1 9 7 12 1 1 1 100 1 9 7 12 1 1 9 12 1 9 12 1 9 12 1 1 11 7 12 1 1 1 9 12 1 1 18 12 1 1 10 7 12 1 1 1 100 1 5 0 11 100 12 1 1 1 11 12 1 1 10 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 9 12 1 9 12 1 11 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 12 1 1 100 1 10 7 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 1 16 1 15 10 12 16 16 15 10 12 16 15 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield sun/rmi/runtime/Log BRIEF Ljava/util/logging/Level; java/util/logging/Level
staticfield sun/rmi/runtime/Log VERBOSE Ljava/util/logging/Level; java/util/logging/Level
staticfield sun/rmi/runtime/Log WALKER Ljava/lang/StackWalker; java/lang/StackWalker
staticfield sun/rmi/runtime/Log logFactory Lsun/rmi/runtime/Log$LogFactory; sun/rmi/runtime/Log$LoggerLogFactory
ciInstanceKlass sun/rmi/runtime/Log$LoggerLog 1 1 178 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 100 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 10 12 1 1 11 100 12 1 1 1 11 12 1 10 100 12 1 1 1 10 12 1 18 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 18 12 1 9 12 1 1 10 12 1 1 100 1 10 12 1 9 10 10 12 1 1 10 12 1 9 12 1 1 100 1 10 12 1 7 1 10 7 1 9 12 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 8 1 15 10 100 12 1 1 1 1 1 100 1 1 1 100 1 100 1 1
staticfield sun/rmi/runtime/Log$LoggerLog alternateConsole Ljava/util/logging/Handler; sun/rmi/runtime/Log$InternalStreamHandler
ciInstanceKlass java/rmi/server/ObjID 1 1 188 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 10 12 1 11 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 7 12 1 1 1 18 12 1 1 18 12 1 18 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 8 1 10 7 12 1 1 10 10 100 1 1 1 1 3 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 8 1 16 1 15 10 12 16 15 10 7 12 1 1 15 10 7 12 1 1 1 1 100 1 100 1 1
staticfield java/rmi/server/ObjID nextObjNum Ljava/util/concurrent/atomic/AtomicLong; java/util/concurrent/atomic/AtomicLong
staticfield java/rmi/server/ObjID mySpace Ljava/rmi/server/UID; java/rmi/server/UID
staticfield java/rmi/server/ObjID secureRandom Ljava/security/SecureRandom; java/security/SecureRandom
instanceKlass sun/rmi/transport/tcp/TCPTransport
ciInstanceKlass sun/rmi/transport/Transport 1 1 369 10 7 12 1 1 1 18 12 1 1 10 7 12 1 1 1 100 1 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 18 12 1 9 12 1 1 10 12 1 11 7 12 1 1 1 10 7 12 1 1 1 100 1 100 1 8 1 10 12 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 9 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 1 10 12 1 9 12 1 8 1 10 12 1 100 1 9 100 12 1 1 10 12 1 1 8 1 10 100 12 1 1 1 18 12 1 1 100 1 18 11 12 1 1 10 12 1 1 11 100 12 1 1 11 12 1 8 1 10 8 1 10 7 12 1 1 10 12 1 10 7 12 1 1 1 9 12 1 1 8 1 8 1 10 12 1 1 10 10 12 1 7 1 10 7 1 8 10 10 12 1 1 7 1 10 12 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 12 16 1 8 1 8 1 15 10 7 12 1 1 1 15 10 100 12 1 1 1 100 1 100 1 1
staticfield sun/rmi/transport/Transport logLevel I -1
staticfield sun/rmi/transport/Transport transportLog Lsun/rmi/runtime/Log; sun/rmi/runtime/Log$LoggerLog
staticfield sun/rmi/transport/Transport currentTransport Ljava/lang/ThreadLocal; java/lang/ThreadLocal
staticfield sun/rmi/transport/Transport dgcID Ljava/rmi/server/ObjID; java/rmi/server/ObjID
staticfield sun/rmi/transport/Transport SETCCL_ACC Ljava/security/AccessControlContext; java/security/AccessControlContext
ciInstanceKlass sun/rmi/transport/tcp/TCPTransport 1 1 612 7 1 10 12 1 1 7 1 10 12 1 7 1 10 12 1 10 7 1 9 7 12 1 1 1 9 12 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 10 12 1 1 100 1 11 7 12 1 1 1 11 100 12 1 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 100 1 10 12 1 7 1 11 100 12 1 1 1 11 10 12 1 7 1 11 12 1 10 12 1 7 1 10 12 1 11 12 1 1 11 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 100 1 10 9 12 1 18 12 1 10 12 1 18 10 100 12 1 1 100 1 18 10 7 12 1 1 1 9 12 1 1 10 7 1 100 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 18 12 1 10 12 1 1 7 1 7 1 10 12 1 18 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 18 10 12 1 18 10 100 12 1 1 18 10 7 1 18 7 1 11 7 12 1 1 1 10 12 1 10 12 1 18 18 12 1 7 1 10 12 1 10 12 1 1 11 18 7 1 11 12 1 1 10 12 1 100 1 10 12 1 11 12 1 10 7 12 1 1 10 7 12 1 1 1 18 10 18 10 12 1 10 12 1 1 100 1 8 1 10 10 12 1 1 8 1 3 10 7 12 1 1 1 8 1 5 0 10 7 12 1 1 1 8 1 3 8 1 10 12 1 10 7 12 1 1 8 1 8 1 18 12 1 1 10 7 12 1 1 1 10 12 1 1 18 10 12 1 9 12 1 18 10 12 1 1 9 12 1 1 7 1 9 7 12 1 1 1 7 1 10 7 1 10 10 12 1 9 12 1 1 7 1 10 9 12 1 1 10 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 18 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 16 15 10 12 16 15 10 12 16 15 10 12 16 15 10 12 15 10 7 12 1 1 15 10 7 12 1 1 1 1 1 1 100 1 100 1 1
staticfield sun/rmi/transport/tcp/TCPTransport tcpLog Lsun/rmi/runtime/Log; sun/rmi/runtime/Log$LoggerLog
staticfield sun/rmi/transport/tcp/TCPTransport maxConnectionThreads I 2147483647
staticfield sun/rmi/transport/tcp/TCPTransport threadKeepAliveTime J 60000
staticfield sun/rmi/transport/tcp/TCPTransport connectionThreadPool Ljava/util/concurrent/ExecutorService; java/util/concurrent/ThreadPoolExecutor
staticfield sun/rmi/transport/tcp/TCPTransport connectionCount Ljava/util/concurrent/atomic/AtomicInteger; java/util/concurrent/atomic/AtomicInteger
staticfield sun/rmi/transport/tcp/TCPTransport threadConnectionHandler Ljava/lang/ThreadLocal; java/lang/ThreadLocal
staticfield sun/rmi/transport/tcp/TCPTransport NOPERMS_ACC Ljava/security/AccessControlContext; java/security/AccessControlContext
staticfield sun/rmi/transport/tcp/TCPTransport defaultSocketFactory Ljava/rmi/server/RMISocketFactory; sun/rmi/transport/tcp/TCPDirectSocketFactory
staticfield sun/rmi/transport/tcp/TCPTransport connectionReadTimeout I 7200000
staticfield sun/rmi/transport/tcp/TCPTransport $assertionsDisabled Z 1
ciInstanceKlass sun/rmi/transport/Target 1 1 407 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 9 7 12 1 1 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 7 12 1 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 18 10 12 1 1 10 12 1 10 12 1 18 7 1 7 1 18 12 1 1 9 12 1 18 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 100 1 10 10 12 1 10 7 12 1 1 100 1 8 1 10 12 1 100 1 8 1 10 9 12 1 18 10 12 1 1 18 12 1 10 12 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 8 1 8 1 16 15 10 12 8 1 8 1 16 15 10 12 16 1 15 10 7 12 1 1 15 10 7 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass sun/rmi/transport/WeakRef 1 1 104 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 8 1 15 10 100 12 1 1 1 100 1 100 1 1
ciInstanceKlass sun/rmi/transport/ObjectTable 1 1 307 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 11 7 12 1 1 1 7 1 9 12 1 7 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 10 12 1 1 10 12 1 11 12 1 1 100 1 8 1 10 8 1 11 12 1 1 10 12 1 1 10 12 1 18 11 12 1 10 12 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 7 1 10 8 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 100 1 10 10 100 12 1 1 8 1 5 0 10 7 12 1 1 1 18 12 1 1 10 12 1 1 7 1 10 7 1 10 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 8 1 16 1 15 10 12 16 15 10 100 12 1 1 15 10 7 12 1 1 1 1 1 100 1 100 1 1
staticfield sun/rmi/transport/ObjectTable gcInterval J 3600000
staticfield sun/rmi/transport/ObjectTable tableLock Ljava/lang/Object; java/lang/Object
staticfield sun/rmi/transport/ObjectTable objTable Ljava/util/Map; java/util/HashMap
staticfield sun/rmi/transport/ObjectTable implTable Ljava/util/Map; java/util/HashMap
staticfield sun/rmi/transport/ObjectTable keepAliveLock Ljava/lang/Object; java/lang/Object
staticfield sun/rmi/transport/ObjectTable reapQueue Ljava/lang/ref/ReferenceQueue; java/lang/ref/ReferenceQueue
ciInstanceKlass sun/rmi/transport/ObjectEndpoint 1 1 66 10 7 12 1 1 1 100 1 10 9 7 12 1 1 1 7 1 10 12 1 10 12 1 1 100 1 10 9 12 1 1 9 12 1 1 10 12 1 1 10 10 12 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
staticfield sun/rmi/transport/ObjectEndpoint $assertionsDisabled Z 1
ciInstanceKlass sun/rmi/transport/tcp/TCPTransport$ConnectionHandler 1 1 391 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 100 1 10 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 10 12 1 11 12 1 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 18 12 1 1 10 12 1 1 18 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 9 12 1 1 10 7 12 1 1 10 12 1 1 100 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 1 100 1 3 9 12 1 1 9 7 12 1 1 1 10 12 1 1 18 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 7 1 10 18 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 9 12 1 18 12 1 10 12 1 10 12 1 10 12 1 10 12 1 18 18 12 1 100 1 8 1 10 12 1 10 12 1 100 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 8 1 16 1 15 10 12 16 8 1 8 1 8 1 8 1 8 1 15 10 7 12 1 1 15 10 7 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass sun/nio/ch/Util$1 1 1 55 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass sun/rmi/transport/tcp/TCPConnection 1 1 201 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 100 1 5 0 9 12 1 1 5 0 9 12 1 5 0 9 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 10 100 1 100 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 1 8 1 10 12 1 1 9 12 1 8 1 10 12 1 5 0 10 12 1 1 8 1 18 12 1 1 10 100 12 1 1 1 18 12 1 10 12 1 10 10 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 8 1 8 1 15 10 100 12 1 1 1 100 1 100 1 1
ciInstanceKlass java/rmi/server/RemoteCall 1 0 28 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1
ciInstanceKlass sun/rmi/transport/StreamRemoteCall 1 1 369 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 7 12 1 1 1 9 7 12 1 1 1 8 1 10 12 1 1 11 7 12 1 1 1 100 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 10 7 12 1 1 10 12 1 1 100 1 100 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 11 12 1 100 1 8 1 10 12 1 8 1 7 1 11 12 1 1 10 12 1 18 12 1 1 10 7 12 1 1 1 10 100 1 10 12 1 10 12 1 11 12 1 10 12 1 100 1 8 1 10 7 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 7 1 10 10 12 1 1 9 12 1 10 12 1 1 18 12 1 1 100 1 8 1 10 10 12 1 10 10 12 1 10 100 12 1 1 8 1 10 10 12 1 1 100 1 10 12 1 8 1 10 12 1 1 8 1 18 8 1 10 12 1 1 100 1 10 10 100 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 11 12 1 1 11 100 12 1 1 1 100 1 10 12 1 1 10 12 1 1 18 12 1 10 12 1 10 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 16 15 10 12 16 8 1 8 1 8 1 15 10 7 12 1 1 1 15 10 100 12 1 1 1 100 1 100 1 1
ciInstanceKlass java/io/ObjectInput 1 0 27 100 1 100 1 100 1 100 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass sun/rmi/transport/ConnectionInputStream 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 7 1 7 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 7 1 11 12 1 10 7 12 1 1 1 9 100 12 1 1 1 9 100 12 1 1 1 8 1 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 100 1 11 12 1 1 10 12 1 100 1 10 12 1 10 10 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 100 1 100 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass  @bci sun/rmi/transport/Transport setContextClassLoader (Ljava/lang/ClassLoader;)V 1 <appendix> member <vmtarget> ; 1 1 26 1 7 1 7 1 100 1 1 1 1 1 12 10 12 9 1 1 1 7 1 1 12 10 1 1
ciInstanceKlass sun/rmi/transport/Transport$1 1 1 70 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 12 1 1 1 1
ciInstanceKlass java/io/ObjectOutput 1 0 23 100 1 100 1 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/rmi/NoSuchObjectException 0 0 23 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass  @bci sun/rmi/transport/StreamRemoteCall getInputStream ()Ljava/io/ObjectInput; 46 <appendix> member <vmtarget> ; 1 1 26 1 7 1 7 1 100 1 1 1 1 1 12 10 12 9 1 1 1 7 1 1 12 10 1 1
ciInstanceKlass java/security/PrivilegedActionException 0 0 134 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 1 8 1 10 100 12 1 1 10 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 100 1 10 12 1 9 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1
ciMethodData java/lang/Object <init> ()V 2 169809 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/HashMap getNode (Ljava/lang/Object;)Ljava/util/HashMap$Node; 2 6042 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 4 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 103 0x60007 0x45 0x2e8 0x1755 0xe0007 0x0 0x2c8 0x1755 0x170002 0x1755 0x210007 0xb8 0x298 0x169d 0x2a0007 0x316 0xb8 0x1387 0x350007 0x72b 0x98 0xc5c 0x390007 0x0 0x78 0xc5c 0x80000004003f0005 0xc 0x0 0x19a23f97d98 0xc53 0x19a65126588 0x1 0x420007 0x0 0x20 0xc60 0x4e0007 0x15 0x1c0 0x301 0x520004 0xfffffffffffffcff 0x0 0x19a651260d0 0x4 0x0 0x0 0x550007 0x301 0x90 0x0 0x590004 0x0 0x0 0x0 0x0 0x0 0x0 0x5f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6a0007 0x1a 0xb8 0x2f7 0x760007 0xe7 0x98 0x210 0x7a0007 0x0 0x78 0x210 0x800005 0x0 0x0 0x19a23f97d98 0x20e 0x19a65126638 0x2 0x830007 0x0 0x20 0x210 0x910007 0x10 0xffffffffffffff48 0xa 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 5 29 java/lang/String 31 java/util/zip/ZipFile$Source$Key 44 java/util/HashMap$Node 81 java/lang/String 83 com/jetbrains/internal/ProxyRepository$Key methods 0
ciMethodData java/util/HashMap get (Ljava/lang/Object;)Ljava/lang/Object; 2 9017 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x20005 0x2339 0x0 0x0 0x0 0x0 0x0 0x70007 0x1f5d 0x38 0x3dc 0xb0003 0x3dc 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/ref/Reference reachabilityFence (Ljava/lang/Object;)V 2 65829 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/security/AccessController doPrivileged (Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;)Ljava/lang/Object; 1 570 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x2 0x23a 0x60002 0x23a 0xd0002 0x23a 0x160002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/security/AccessController executePrivileged (Ljava/security/PrivilegedAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object; 2 1861 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 52 0x10007 0x4ed 0x30 0x258 0x50002 0x258 0xb0007 0x745 0x60 0x0 0xe0002 0x0 0x110007 0x0 0x30 0x0 0x180002 0x0 0x1d0005 0x6f7 0x0 0x19a658226f8 0x49 0x19a658227a8 0x2 0x260007 0x743 0x60 0x0 0x290002 0x0 0x2c0007 0x0 0x30 0x0 0x330002 0x0 0x380002 0x743 0x3c0002 0x743 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 2 21 java/lang/Class$1 23 org/bouncycastle/util/Properties$1 methods 0
ciMethodData java/lang/invoke/DirectMethodHandle allocateInstance (Ljava/lang/Object;)Ljava/lang/Object; 2 1815 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x10004 0x0 0x0 0x19a6528dc68 0x655 0x0 0x0 0xc0005 0x717 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/lang/invoke/DirectMethodHandle$Constructor methods 0
ciMethodData java/lang/invoke/DirectMethodHandle constructorMethod (Ljava/lang/Object;)Ljava/lang/Object; 2 1814 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10004 0x0 0x0 0x19a6528dc68 0x654 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/lang/invoke/DirectMethodHandle$Constructor methods 0
ciMethod java/rmi/server/RemoteServer getClientHost ()Ljava/lang/String; 0 0 1 0 -1
ciMethod sun/rmi/server/UnicastServerRef clearStackTraces (Ljava/lang/Throwable;)V 0 0 1 0 -1
ciMethod sun/rmi/runtime/Log isLoggable (Ljava/util/logging/Level;)Z 0 0 1 0 -1
ciMethod sun/rmi/runtime/Log log (Ljava/util/logging/Level;Ljava/lang/String;)V 0 0 1 0 -1
ciMethod sun/rmi/runtime/Log log (Ljava/util/logging/Level;Ljava/lang/String;Ljava/lang/Throwable;)V 0 0 1 0 -1
ciMethod sun/rmi/runtime/Log$LoggerLog isLoggable (Ljava/util/logging/Level;)Z 512 0 2688 0 -1
ciMethod sun/rmi/runtime/Log$LoggerLog log (Ljava/util/logging/Level;Ljava/lang/String;)V 512 0 804 0 -1
ciMethod sun/rmi/runtime/Log$LoggerLog log (Ljava/util/logging/Level;Ljava/lang/String;Ljava/lang/Throwable;)V 0 0 1 0 -1
ciMethod java/rmi/server/ObjID read (Ljava/io/ObjectInput;)Ljava/rmi/server/ObjID; 512 0 257 0 -1
ciMethod java/rmi/server/ObjID equals (Ljava/lang/Object;)Z 768 0 636 0 -1
ciMethod sun/rmi/transport/tcp/TCPTransport getClientHost ()Ljava/lang/String; 0 0 1 0 -1
ciMethod sun/rmi/transport/Transport setContextClassLoader (Ljava/lang/ClassLoader;)V 512 0 450 0 -1
ciMethod sun/rmi/transport/Transport serviceCall (Ljava/rmi/server/RemoteCall;)Z 512 0 256 0 -1
ciMethodData java/lang/invoke/Invokers$Holder linkToTargetMethod (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 1152 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 34 0x10004 0x0 0x0 0x19a6528df10 0x68 0x19a6528dc68 0x20f 0x5000b 0x480 0x0 0x0 0x0 0x0 0x0 0x3 0x1 0x2 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 0xc 0x4 0x0 0x2 0x1 0x2 oops 2 3 java/lang/invoke/BoundMethodHandle$Species_LL 5 java/lang/invoke/DirectMethodHandle$Constructor methods 0
ciMethod sun/rmi/transport/Target getDispatcher ()Lsun/rmi/server/Dispatcher; 256 0 128 0 -1
ciMethod sun/rmi/transport/Target getAccessControlContext ()Ljava/security/AccessControlContext; 256 0 128 0 -1
ciMethod sun/rmi/transport/Target getContextClassLoader ()Ljava/lang/ClassLoader; 256 0 128 0 -1
ciMethod sun/rmi/transport/Target getImpl ()Ljava/rmi/Remote; 768 0 387 0 -1
ciMethod sun/rmi/transport/Target incrementCallCount ()V 512 0 256 0 -1
ciMethod sun/rmi/transport/Target decrementCallCount ()V 512 0 256 0 -1
ciMethod sun/rmi/transport/ObjectTable getTarget (Lsun/rmi/transport/ObjectEndpoint;)Lsun/rmi/transport/Target; 512 0 337 0 -1
ciMethod sun/rmi/transport/ObjectEndpoint <init> (Ljava/rmi/server/ObjID;Lsun/rmi/transport/Transport;)V 512 0 395 0 -1
ciMethod sun/rmi/transport/tcp/TCPTransport$ConnectionHandler getClientHost ()Ljava/lang/String; 0 0 1 0 -1
ciMethod java/rmi/server/RemoteCall getInputStream ()Ljava/io/ObjectInput; 0 0 1 0 -1
ciMethod java/rmi/server/RemoteCall getResultStream (Z)Ljava/io/ObjectOutput; 0 0 1 0 -1
ciMethod sun/rmi/transport/StreamRemoteCall getInputStream ()Ljava/io/ObjectInput; 512 0 724 0 -1
ciMethod sun/rmi/transport/StreamRemoteCall getResultStream (Z)Ljava/io/ObjectOutput; 512 0 256 0 -1
ciMethod  @bci sun/rmi/transport/Transport setContextClassLoader (Ljava/lang/ClassLoader;)V 1 <appendix> member <vmtarget> ; <init> (Ljava/lang/ClassLoader;)V 512 0 450 0 -1
ciMethod sun/rmi/transport/Transport$1 <init> (Lsun/rmi/transport/Transport;Ljava/security/AccessControlContext;Lsun/rmi/server/Dispatcher;Ljava/rmi/Remote;Ljava/rmi/server/RemoteCall;)V 512 0 256 0 -1
ciMethod java/rmi/NoSuchObjectException <init> (Ljava/lang/String;)V 0 0 1 0 -1
ciMethodData java/security/AccessController checkContext (Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/security/AccessControlContext; 1 929 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 56 0x2 0x3a1 0x30007 0x3a1 0x160 0x0 0x70007 0x0 0x140 0x0 0xb0005 0x0 0x0 0x0 0x0 0x0 0x0 0xe0007 0x0 0xe8 0x0 0x120002 0x0 0x150007 0x0 0xb8 0x0 0x190002 0x0 0x1e0007 0x0 0x88 0x0 0x250005 0x0 0x0 0x0 0x0 0x0 0x0 0x280007 0x0 0x30 0x0 0x2b0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethod java/security/PrivilegedActionException getException ()Ljava/lang/Exception; 0 0 1 0 -1
ciMethodData java/lang/ThreadLocal getMap (Ljava/lang/Thread;)Ljava/lang/ThreadLocal$ThreadLocalMap; 2 3823 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 6 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/invoke/DirectMethodHandle$Holder newInvokeSpecial (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 1 565 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x1000a 0x235 0x3 0x0 0x19a6528dc68 0x2 0x6000a 0x235 0x3 0x0 0x19a6528dc68 0x19a23f9a508 0xd0004 0x0 0x0 0x19a23f9a508 0x81 0x0 0x0 0x10000a 0x235 0x4 0x0 0x2 0x1 0x3 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 0xc 0x4 0x0 0x19a6528dc68 0x1 0x3 oops 5 4 java/lang/invoke/DirectMethodHandle$Constructor 10 java/lang/invoke/DirectMethodHandle$Constructor 11 java/lang/invoke/MemberName 15 java/lang/invoke/MemberName 39 java/lang/invoke/DirectMethodHandle$Constructor methods 0
ciMethodData java/lang/ThreadLocal get (Ljava/lang/Thread;)Ljava/lang/Object; 2 3287 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 39 0x20005 0x28 0x0 0x19a65256f40 0x2b 0x19a65256ff0 0xc84 0x70007 0x1 0x78 0xcd6 0xc0005 0xcd6 0x0 0x0 0x0 0x0 0x0 0x110007 0x1 0x20 0xcd7 0x1f0005 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 3 jdk/internal/jimage/ImageBufferCache$1 5 sun/nio/ch/Util$1 methods 0
ciMethodData java/lang/ThreadLocal get ()Ljava/lang/Object; 1 39 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x10002 0x27 0x40005 0x27 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/logging/Logger isLoggable (Ljava/util/logging/Level;)Z 2 2788 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x90005 0xae4 0x0 0x0 0x0 0x0 0x0 0xd0007 0xae4 0x40 0x0 0x140007 0x0 0x20 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData sun/rmi/runtime/Log$LoggerLog isLoggable (Ljava/util/logging/Level;)Z 2 2432 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x50005 0x0 0x0 0x19a65253768 0x980 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 3 java/util/logging/Logger methods 0
ciMethodData sun/rmi/runtime/Log$LoggerLog log (Ljava/util/logging/Level;Ljava/lang/String;)V 1 548 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 62 0x20005 0x0 0x0 0x19a65122c48 0x224 0x0 0x0 0x50007 0x224 0x160 0x0 0x80002 0x0 0x120005 0x0 0x0 0x0 0x0 0x0 0x0 0x180005 0x0 0x0 0x0 0x0 0x0 0x0 0x1d0002 0x0 0x200005 0x0 0x0 0x0 0x0 0x0 0x0 0x24000a 0x0 0x5 0x0 0x0 0x1 0x0 0x19a23f97d98 0x290005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 2 3 sun/rmi/runtime/Log$LoggerLog 43 java/lang/String methods 0
ciMethodData sun/rmi/transport/StreamRemoteCall getInputStream ()Ljava/io/ObjectInput; 1 468 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 41 0x40007 0x13d 0x100 0x97 0xf0005 0x0 0x0 0x19a65122c48 0x97 0x0 0x0 0x1b0005 0x0 0x0 0x19a65122cf8 0x97 0x0 0x0 0x200002 0x97 0x2a0007 0x95 0x60 0x2 0x2e000a 0x2 0x3 0x0 0x19a65122828 0x19a65122da8 0x330002 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 4 7 sun/rmi/runtime/Log$LoggerLog 14 sun/rmi/transport/tcp/TCPConnection 28 sun/rmi/transport/StreamRemoteCall 29  @bci sun/rmi/transport/StreamRemoteCall getInputStream ()Ljava/io/ObjectInput; 46 <appendix> member <vmtarget> ; methods 0
ciMethodData java/lang/ThreadLocal$ThreadLocalMap set (Ljava/lang/ThreadLocal;Ljava/lang/Object;)V 1 274 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 80 0x1c0007 0x1 0x130 0x111 0x220005 0x111 0x0 0x0 0x0 0x0 0x0 0x250007 0x0 0x20 0x111 0x320005 0x0 0x0 0x0 0x0 0x0 0x0 0x350007 0x0 0x58 0x0 0x3d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x460002 0x0 0x4f0003 0x0 0xfffffffffffffee8 0x5b0002 0x1 0x5e0004 0x0 0x0 0x19a65826998 0x1 0x0 0x0 0x710005 0x1 0x0 0x0 0x0 0x0 0x0 0x740007 0x0 0x78 0x1 0x7d0007 0x1 0x58 0x0 0x810005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 1 43 java/lang/ThreadLocal$ThreadLocalMap$Entry methods 0
ciMethodData java/security/AccessController doPrivileged (Ljava/security/PrivilegedAction;Ljava/security/AccessControlContext;)Ljava/lang/Object; 1 345 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x2 0x159 0x60002 0x159 0xd0002 0x159 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/ThreadLocal set (Ljava/lang/Thread;Ljava/lang/Object;)V 1 263 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 39 0x20005 0x0 0x0 0x19a65824618 0x107 0x0 0x0 0x70007 0x0 0x70 0x107 0xd0005 0x107 0x0 0x0 0x0 0x0 0x0 0x100003 0x107 0x50 0x160005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 1 3 java/lang/ThreadLocal methods 0
ciMethodData java/lang/ThreadLocal createMap (Ljava/lang/Thread;Ljava/lang/Object;)V 1 1 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x70002 0x1 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/ThreadLocal set (Ljava/lang/Object;)V 1 261 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x10002 0x105 0x50005 0x105 0x0 0x0 0x0 0x0 0x0 0xb0007 0x105 0x30 0x0 0xe0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData sun/rmi/transport/Transport setContextClassLoader (Ljava/lang/ClassLoader;)V 1 194 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x1000a 0xc2 0x3 0x0 0x2 0x19a6528d538 0x90002 0xc2 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 5  @bci sun/rmi/transport/Transport setContextClassLoader (Ljava/lang/ClassLoader;)V 1 <appendix> member <vmtarget> ; methods 0
ciMethodData  @bci sun/rmi/transport/Transport setContextClassLoader (Ljava/lang/ClassLoader;)V 1 <appendix> member <vmtarget> ; <init> (Ljava/lang/ClassLoader;)V 1 194 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x10002 0xc2 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/rmi/server/ObjID equals (Ljava/lang/Object;)Z 1 252 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 46 0x10004 0x0 0x0 0x19a65122e78 0xfc 0x0 0x0 0x40007 0x0 0xe8 0xfc 0x80004 0x0 0x0 0x19a65122e78 0xfc 0x0 0x0 0x150007 0x5b 0x90 0xa1 0x200005 0xa1 0x0 0x0 0x0 0x0 0x0 0x230007 0x0 0x38 0xa1 0x270003 0xa1 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 3 java/rmi/server/ObjID 14 java/rmi/server/ObjID methods 0
ciMethodData java/lang/Thread getContextClassLoader ()Ljava/lang/ClassLoader; 1 169 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x60007 0xa9 0x20 0x0 0xb0002 0xa9 0x100007 0xa9 0x40 0x0 0x130002 0x0 0x190002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData sun/rmi/transport/ObjectEndpoint <init> (Ljava/rmi/server/ObjID;Lsun/rmi/transport/Transport;)V 1 139 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x10002 0x8b 0x50007 0x8b 0x30 0x0 0xc0002 0x0 0x130007 0x8b 0xb8 0x0 0x170007 0x0 0x98 0x0 0x200002 0x0 0x230005 0x0 0x0 0x0 0x0 0x0 0x0 0x260007 0x0 0x30 0x0 0x2d0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData sun/rmi/transport/ObjectTable getTarget (Lsun/rmi/transport/ObjectEndpoint;)Lsun/rmi/transport/Target; 1 81 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0xa0005 0x0 0x0 0x19a65124430 0x51 0x0 0x0 0xf0004 0x0 0x0 0x19a651241f8 0x51 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 2 3 java/util/HashMap 10 sun/rmi/transport/Target methods 0
ciMethodData sun/rmi/transport/Target getImpl ()Ljava/rmi/Remote; 1 3 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x40005 0x0 0x0 0x19a65126bc8 0x3 0x0 0x0 0x70004 0x0 0x0 0x19a65126c78 0x1 0x19a65126d28 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 3 3 sun/rmi/transport/WeakRef 10 sun/rmi/registry/RegistryImpl 12 com/intellij/execution/rmi/IdeaWatchdogImpl methods 0
ciMethodData java/rmi/server/ObjID read (Ljava/io/ObjectInput;)Ljava/rmi/server/ObjID; 1 1 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 20 0x10005 0x0 0x0 0x19a65123230 0x1 0x0 0x0 0x80002 0x1 0x120002 0x1 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 sun/rmi/transport/ConnectionInputStream methods 0
ciMethodData sun/rmi/transport/Transport serviceCall (Ljava/rmi/server/RemoteCall;)Z 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 272 0x10005 0x0 0x0 0x0 0x0 0x0 0x0 0x60002 0x0 0xa0003 0x0 0x28 0x170002 0x0 0x1f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x220007 0x0 0x38 0x0 0x260003 0x0 0x18 0x330002 0x0 0x360002 0x0 0x3d0007 0x0 0x78 0x0 0x420005 0x0 0x0 0x0 0x0 0x0 0x0 0x470007 0x0 0x30 0x0 0x500002 0x0 0x560005 0x0 0x0 0x0 0x0 0x0 0x0 0x5d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x680005 0x0 0x0 0x0 0x0 0x0 0x0 0x6d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x740005 0x0 0x0 0x0 0x0 0x0 0x0 0x790002 0x0 0x7c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x850007 0x0 0x30 0x0 0x8a0002 0x0 0x910005 0x0 0x0 0x0 0x0 0x0 0x0 0x9f0002 0x0 0xa40002 0x0 0xa80003 0x0 0x88 0xaf0005 0x0 0x0 0x0 0x0 0x0 0x0 0xb20004 0x0 0x0 0x0 0x0 0x0 0x0 0xba0007 0x0 0x30 0x0 0xbf0002 0x0 0xc60005 0x0 0x0 0x0 0x0 0x0 0x0 0xc90003 0x0 0x80 0xd20007 0x0 0x30 0x0 0xd70002 0x0 0xde0005 0x0 0x0 0x0 0x0 0x0 0x0 0xe60005 0x0 0x0 0x0 0x0 0x0 0x0 0xe90003 0x0 0xc0 0xf80005 0x0 0x0 0x0 0x0 0x0 0x0 0x1000005 0x0 0x0 0x0 0x0 0x0 0x0 0x10a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1100003 0x0 0x238 0x11a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x11d0007 0x0 0xe0 0x0 0x1230002 0x0 0x126000a 0x0 0x3 0x0 0x0 0x0 0x12c0003 0x0 0x18 0x132000a 0x0 0x3 0x0 0x0 0x0 0x1420005 0x0 0x0 0x0 0x0 0x0 0x0 0x1470005 0x0 0x0 0x0 0x0 0x0 0x0 0x14e0002 0x0 0x1530005 0x0 0x0 0x0 0x0 0x0 0x0 0x1590005 0x0 0x0 0x0 0x0 0x0 0x0 0x15e0003 0x0 0x50 0x16b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData sun/rmi/transport/Target incrementCallCount ()V 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x40007 0x0 0x38 0x0 0x110003 0x0 0x28 0x1b0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData sun/rmi/transport/Transport$1 <init> (Lsun/rmi/transport/Transport;Ljava/security/AccessControlContext;Lsun/rmi/server/Dispatcher;Ljava/rmi/Remote;Ljava/rmi/server/RemoteCall;)V 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x1c0002 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData sun/rmi/runtime/Log$LoggerLog log (Ljava/util/logging/Level;Ljava/lang/String;Ljava/lang/Throwable;)V 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 63 0x20005 0x0 0x0 0x0 0x0 0x0 0x0 0x50007 0x0 0x160 0x0 0x80002 0x0 0x140005 0x0 0x0 0x0 0x0 0x0 0x0 0x1b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x200002 0x0 0x230005 0x0 0x0 0x0 0x0 0x0 0x0 0x27000a 0x0 0x5 0x0 0x0 0x1 0x0 0x0 0x2d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData sun/rmi/transport/Target decrementCallCount ()V 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 29 0xb0007 0x0 0x30 0x0 0x150002 0x0 0x1d0007 0x0 0x70 0x0 0x240007 0x0 0x50 0x0 0x2b0007 0x0 0x30 0x0 0x2e0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/rmi/server/RemoteServer getClientHost ()Ljava/lang/String; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData sun/rmi/transport/tcp/TCPTransport getClientHost ()Ljava/lang/String; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 35 0x30005 0x0 0x0 0x0 0x0 0x0 0x0 0x60004 0x0 0x0 0x0 0x0 0x0 0x0 0xb0007 0x0 0x58 0x0 0xf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1a0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData sun/rmi/transport/StreamRemoteCall getResultStream (Z)Ljava/io/ObjectOutput; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 67 0x40007 0x0 0x30 0x0 0xd0002 0x0 0x1e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x230002 0x0 0x2a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x340007 0x0 0x70 0x0 0x3c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3f0003 0x0 0x50 0x470005 0x0 0x0 0x0 0x0 0x0 0x0 0x4e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData sun/rmi/server/UnicastServerRef clearStackTraces (Ljava/lang/Throwable;)V 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x60007 0x0 0xa8 0x0 0xb0005 0x0 0x0 0x0 0x0 0x0 0x0 0xf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x130003 0x0 0xffffffffffffff70 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Throwable setStackTrace ([Ljava/lang/StackTraceElement;)V 0 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 154 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 78